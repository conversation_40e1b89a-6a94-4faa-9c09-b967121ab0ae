import React, { useEffect, useMemo, useRef, useState } from 'react'
import styles from './SellerListTab.module.scss';
import dayjs from 'dayjs';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import { Fade, MenuItem, Select, Tooltip, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import { localStorageKeys, purchaseOrder, routes, sellerViewConstants } from 'src/renderer2/common';
import SavedSearchList from '../Templates/SavedPricingTemplate';
import clsx from 'clsx';
import { useLeftPanelStore } from '../LeftPanelStore';
import { useSearchStore } from 'src/renderer2/store/SearchStore';
import { useBuyerSettingStore, useSellerOrderStore, useSellerSettingStore } from '@bryzos/giss-ui-library';
import { clearLocal, formatToTwoDecimalPlaces, getLocal, navigatePage, newPriceFormatter } from 'src/renderer2/helper';
import usePostSaveSearchProducts from 'src/renderer2/hooks/usePostSaveSearchProducts';
import { ReactComponent as CreateNewButton } from '../../../assets/New-images/New-Image-latest/create-new-button.svg';
import { ReactComponent as CreateNewButtonHover } from '../../../assets/New-images/New-Image-latest/create-new-button-hover.svg';
import { ReactComponent as DeleteIcon } from '../../../assets/New-images/New-Image-latest/delete-outlined.svg';
import { ReactComponent as SettingsIcon } from '../../../assets/New-images/New-Image-latest/settings-outlined.svg';
import { ReactComponent as ArrowIcon } from '../../../assets/New-images/New-Image-latest/Polygon.svg';
import axios from 'axios';
import { useGlobalStore } from '@bryzos/giss-ui-library';
import { getPurchaseOrderFilteredList } from 'src/renderer2/helper/commonFunctionality';
import moment from 'moment';
import { AcceptTooltip, activeOrderTooltip, pendingOrderTooltip } from 'src/renderer2/tooltip';
import SellerListItemTemplate from '../Templates/SellerListItemTemplate';
import { useGlobalSearchStore } from 'src/renderer2/pages/GlobalSearchField/globalSearchStore';

dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

// Define types for better TypeScript support
interface SearchProduct {
  id: string;
  title: string;
  created_date: string;
  time_stamp?: string;
  search_date_time: string;
  item_count: number;
  amount?: number;
  name?: string;
  products: Array<{ shape?: string }>;
  order_size: number;
}

interface GroupedData {
  [key: string]: SearchProduct[];
}

// Unified grouping function that works for all filters
const groupByFilteredAndAll = (items: SearchProduct[] , states: any , isOrderClaimPreference: boolean): any => {
  const groups: GroupedData = {
    'Filtered': [],
    'All': []
  };

  items?.forEach((item: SearchProduct) => {
    // Add to All group
    // Add to Filtered group if state_id matches 11
    if (isOrderClaimPreference && states.includes(Number(item.state_id))) {
      groups['Filtered'].push(item);
    }else{
      groups['All'].push(item);
    }
  });

  // Remove empty groups
  // Object.keys(groups).forEach(key => {
  //   if (groups[key].length === 0) {
  //     delete groups[key];
  //   }
  // });

  // Always keep Filtered first, regardless of sort order
  const sortedGroups: GroupedData = {};
  
  // Always put Filtered first, then All
  ['Filtered', 'All'].forEach(groupName => {
    if (groups[groupName]) {
      sortedGroups[groupName] = groups[groupName];
    }
  });

  return sortedGroups;
};
// Filter functions for each menu item - all using the same grouping system
const filterByNewest = (data: SearchProduct[], field: string = 'created_date' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group by the specified field (newest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      switch (field) {
        case 'created_date':
          return new Date(b.created_date).getTime() - new Date(a.created_date).getTime();
        case 'time_stamp':
          return new Date(b.time_stamp || b.created_date).getTime() - new Date(a.time_stamp || a.created_date).getTime();
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return new Date(b.created_date).getTime() - new Date(a.created_date).getTime();
      }
    });
  });
  
  return sortedGrouped;
};

const filterByOldest = (data: SearchProduct[], field: string = 'created_date' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group by the specified field (oldest first)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      switch (field) {
        case 'created_date':
          return new Date(a.created_date).getTime() - new Date(b.created_date).getTime();
        case 'time_stamp':
          return new Date(a.time_stamp || a.created_date).getTime() - new Date(b.time_stamp || b.created_date).getTime();
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return new Date(a.created_date).getTime() - new Date(b.created_date).getTime();
      }
    });
  });
  
  return sortedGrouped;
};

const filterByAToZ = (data: SearchProduct[], field: string = 'title' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group alphabetically by the specified field (A to Z)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'title':
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
        case 'created_date':
          return new Date(a.created_date).toISOString().localeCompare(new Date(b.created_date).toISOString());
        case 'time_stamp':
          return new Date(a.time_stamp || a.created_date).toISOString().localeCompare(new Date(b.time_stamp || b.created_date).toISOString());
        default:
          return (a.title || '').toLowerCase().localeCompare((b.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByZToA = (data: SearchProduct[], field: string = 'title' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group alphabetically by the specified field (Z to A)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'title':
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
        case 'created_date':
          return new Date(b.created_date).toISOString().localeCompare(new Date(a.created_date).toISOString());
        case 'time_stamp':
          return new Date(b.time_stamp || b.created_date).toISOString().localeCompare(new Date(a.time_stamp || a.created_date).toISOString());
        default:
          return (b.title || '').toLowerCase().localeCompare((a.title || '').toLowerCase());
      }
    });
  });
  
  return sortedGrouped;
};

const filterByHighest = (data: SearchProduct[], field: string = 'amount' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group by the specified field (highest to lowest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'item_count':
          return (b.item_count || 0) - (a.item_count || 0);
        case 'order_size':
          return (b.order_size || 0) - (a.order_size || 0);
        default:
          return (b.item_count || 0) - (a.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const filterByLowest = (data: SearchProduct[], field: string = 'amount' , orderFullfillmentStates: any , isOrderClaimPreference: boolean): GroupedData => {
  const grouped = groupByFilteredAndAll(data, orderFullfillmentStates, isOrderClaimPreference);
  
  // Sort each group by the specified field (lowest to highest)
  const sortedGrouped: GroupedData = {};
  
  Object.keys(grouped).forEach(label => {
    sortedGrouped[label] = grouped[label].sort((a, b) => {
      let aValue, bValue;
      
      switch (field) {
        case 'item_count':
          return (a.item_count || 0) - (b.item_count || 0);
        case 'order_size':
          return (a.order_size || 0) - (b.order_size || 0);
        default:
          return (a.item_count || 0) - (b.item_count || 0);
      }
    });
  });
  
  return sortedGrouped;
};

const ListTab = () => {
  const location = useLocation();
  const [grouped, setGrouped] = useState<GroupedData>({
    'Filtered': [],
    'All': [] ,
  });
  const [viewFilter, setViewFilter] = useState<string>('newest');
  const [selectOpen, setSelectOpen] = useState<boolean>(false);
  // Add accordion state for MUI Accordion
  const [expanded, setExpanded] = useState<string | false>('all-panel');
  const purchaseOrdersList = useSellerOrderStore((state: any) => state.ordersCart);
  const filteredPoList = useSellerOrderStore((state: any) => state.filteredPoList);
  const setFilteredPoList = useSellerOrderStore((state: any) => state.setFilteredPoList);
  const { navigationStateForNotification, setNavigationStateForNotification, setViewedOrdersListForBadgeCount, referenceData, productMapping , setShowLoader, viewedOrdersListForBadgeCount , hasLoginProcessCompleted } = useGlobalStore();
  const setOrderToBeShownInOrderAccept = useSellerOrderStore(state => state.setOrderToBeShownInOrderAccept);
  const searchByProductOrZipValue = useSellerOrderStore(state => state.searchByProductOrZipValue);
  const setSearchByProductOrZipValue = useSellerOrderStore(state => state.setSearchByProductOrZipValue);
  const availableAfter = referenceData?.ref_general_settings?.find(setting => setting.name === "SELLER_AVAIL_IN_MINUTES")?.value;
  const filterPoStoreBy = useSellerOrderStore(state => state.filterPoStoreBy);
  const {isRequiredSellerSettingsFilled} = useGlobalStore();
  const [orderFullfillmentStates, setOrderFullfillmentStates] = useState<string[]>([]);
  const [isOrderClaimPreference, setIsOrderClaimPreference] = useState(false);
  const setGroupList = useSellerOrderStore((state: any) => state.setGroupList);
  const {setIsFiltered , setIndex , setShowPopup} = useSellerOrderStore();
  const[disbaleExportPoBtn, setDisbaleExportPoBtn] = useState(false);
  const {sellerSettings} = useSellerSettingStore();
  const orderDetail = useSellerOrderStore(state => state.orderToBeShownInOrderAccept);
  const {finishedListForBadgeCount} = useGlobalStore()
  const stateRef = referenceData?.ref_states;
  const state = useLocation().state;
  const setErrorPopupDetail = useSellerOrderStore(state => state.setErrorPopupDetail);
  const itemRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Single dropdown state
  const [dynamicDropdownOpen, setDynamicDropdownOpen] = useState<boolean>(false);
  const [dynamicDropdownValue, setDynamicDropdownValue] = useState<string>('date-created'); // Set default value
  const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();
  const navigate = useNavigate();

  // Dynamic options based on selected filter
  const getDynamicOptions = (filter: string=viewFilter) => {
    switch (filter) {
      case 'newest':
      case 'oldest':
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
          { value: 'date-modified', label: 'Date Modified', field: 'time_stamp' }
        ];
        
      case 'a-to-z':
      case 'z-to-a':
        return [
          { value: 'list-name', label: 'List Name', field: 'title' , disabled: true  },
          { value: 'product-type', label: 'Product Type', field: 'product_type', disabled: true }
        ];
        
      case 'highest':
      case 'lowest':
        return [
          { value: 'item-count', label: 'Item Count', field: 'item_count',disabled: true  },
          { value: 'order-size', label: 'Order Size', field: 'order_size', disabled: true  }
        ];
        
      default:
        return [
          { value: 'date-created', label: 'Date Created', field: 'created_date' },
        ];
    }
  };

  const dynamicOptions = getDynamicOptions();

  useEffect(() => {
    if(sellerSettings){
      setIsOrderClaimPreference(Boolean(sellerSettings.order_claim_preferences));
      setOrderFullfillmentStates(sellerSettings.order_fulfillment_states);
    }
  }, [sellerSettings]);

  useEffect(() => {
    setShowLoader(true);
    if (purchaseOrdersList) { 
      const poListToBeShown = getPurchaseOrderFilteredList(purchaseOrdersList, filterPoStoreBy, searchByProductOrZipValue, productMapping);
      setFilteredPoList(poListToBeShown);
      setShowLoader(false);
    }
  }, [filterPoStoreBy, searchByProductOrZipValue, purchaseOrdersList]);

  const saveViewedOrdersListForBadgeCount = () => {
    // Start with previous viewed order IDs
    const viewedOrderSet = new Set(viewedOrdersListForBadgeCount);

    // Add current page's viewed orders
    purchaseOrdersList.forEach((currentOrder: any) => {
      const isClaim = currentOrder.claimed_by === purchaseOrder.readyToClaim && location.pathname === routes.orderPage;
      const isPreview = currentOrder.claimed_by === purchaseOrder.pending && location.pathname === routes.previewOrderPage;
  
      if (isClaim || isPreview) {
        viewedOrderSet.add(currentOrder.buyer_po_number);
      }
    });
  
    const finalViewedOrderList = Array.from(viewedOrderSet);
  
    const payload = {
      data: {
        viewedOrdersListForBadgeCount: finalViewedOrderList
      }
    };
    axios
      .post(`${import.meta.env.VITE_API_NOTIFICATION_SERVICE}/notification/saveUserUiEventData`, payload)
      .then(() => {
        setViewedOrdersListForBadgeCount(finalViewedOrderList);
      })
      .catch((error) => {
        console.error('Error updating userJsonData:', error);
      });
  };

  useEffect(() => {
    if (purchaseOrdersList?.length && finishedListForBadgeCount) {
      saveViewedOrdersListForBadgeCount();
    }
  }, [location.pathname, purchaseOrdersList, finishedListForBadgeCount]);

  useEffect(() => {
    if (isRequiredSellerSettingsFilled) {
        // setOpenReminderYouAreAlmostTherePopup(false);
    } else {
        setDisbaleExportPoBtn(true)
        // setOpenReminderYouAreAlmostTherePopup(true);
    }
}, [isRequiredSellerSettingsFilled]);


const handleExportPDfClick = ($event) => {
  $event.stopPropagation();
  if (isRequiredSellerSettingsFilled) {
      // setOpenReminderYouAreAlmostTherePopup(false);
      setDisbaleExportPoBtn(false)
  } else {
      // setIsReminderPopup(false);
      setDisbaleExportPoBtn(true);
      // setOpenReminderYouAreAlmostTherePopup(true);
  }
}

  const sortedData = useMemo(() => {
    // Use savedSearchProducts instead of undefined 'data'
    let data = filteredPoList || [];
    if(location.pathname === routes.homePage) data = filteredPoList

    if(location.pathname === routes.previewOrderPage){
      data = data.filter((item: any) => item.claimed_by === purchaseOrder.pending &&   item.is_order_hidden !== true    )
    }else if(location.pathname === routes.deleteOrderPage){
      data = data.filter((item: any) => item.is_order_hidden === true)
    } else {
      data = data.filter((item: any) => item.claimed_by === purchaseOrder.readyToClaim &&   item.is_order_hidden !== true)
    }
    
    // Apply filtering based on both main filter and dynamic dropdown value\
    let filteredData = data;



    // Map dynamic dropdown value to field name
    const getFieldFromDropdownValue = (dropdownValue: string): string => {
      switch (dropdownValue) {
        case 'date-created':
          return 'created_date';
        case 'date-modified':
          return 'time_stamp';
        case 'list-name':
          return 'title';
        case 'product-type':
          return 'product_type';
        case 'item-count':
          return 'item_count';
        case 'order-size':
          return 'order_size';
        default:
          return 'created_date';
      }
    };

    const fieldToSortBy = getFieldFromDropdownValue(dynamicDropdownValue);

    // Then apply the main filter sorting
    switch (viewFilter) {
      case 'newest':
        return filterByNewest(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);

      case 'oldest':
        return filterByOldest(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
        
      case 'a-to-z':
        return filterByAToZ(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
        
      case 'z-to-a':
        return filterByZToA(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
        
      case 'highest':
        return filterByHighest(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
        
      case 'lowest':
        return filterByLowest(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
        
      default:
        return filterByNewest(filteredData, fieldToSortBy, orderFullfillmentStates, isOrderClaimPreference);
    }
  }, [viewFilter, dynamicDropdownValue, filteredPoList, location.pathname , orderFullfillmentStates, isOrderClaimPreference]);


  useEffect(() => {
    const poNumber = navigationStateForNotification?.referenceId;
    const action = navigationStateForNotification?.action;
    if (poNumber && purchaseOrdersList) {
      if(action === 'previewOrder'){
        navigate(routes.previewOrderPage, { state: {referenceId: poNumber} });
      }else{
        navigate(routes.orderPage, { state: {referenceId: poNumber} });
      }
      setNavigationStateForNotification(null);
    }
  }, [navigationStateForNotification]);

  useEffect(() => {
    if(!state?.referenceId){
      setOrderToBeShownInOrderAccept({})
      setIndex(-1)
    }
  }, [location.pathname])


  useEffect(() => {
    if(state?.referenceId && Object.keys(sortedData).length > 0){
      // Search in sortedData.All and sortedData.Filtered for the item with matching buyer_po_number
      let foundItem = null;
      let foundIndex = -1;
      let isFiltered = false;
      // First search in Filtered array
      if (sortedData.Filtered && sortedData.Filtered.length > 0) {
        foundIndex = sortedData.Filtered.findIndex((item: any) => item.buyer_po_number === state.referenceId);
        if (foundIndex !== -1) {
          foundItem = sortedData.Filtered[foundIndex];
          isFiltered = true;
        }
      }
      // If not found in Filtered, search in All array
      if (!foundItem && sortedData.All && sortedData.All.length > 0) {
        foundIndex = sortedData.All.findIndex((item: any) => item.buyer_po_number === state.referenceId);
        if (foundIndex !== -1) {
          foundItem = sortedData.All[foundIndex];
          isFiltered = false;
        }
      }

      if (foundItem) {
        setIsFiltered(isFiltered)
        setIndex(foundIndex)
        setOrderToBeShownInOrderAccept(foundItem)
        // You can now use foundItem, foundIndex, and isFiltered as needed
      } else {
        setOrderToBeShownInOrderAccept({})
        setIndex(-1)
      }

    }
  }, [state?.referenceId , sortedData])

  useEffect(() => {
    if(Object.keys(sortedData).length > 0){
      setGrouped(sortedData);
      setGroupList(sortedData);
    }else{
      setGrouped({
        'Filtered': [],
        'All': [] ,
      });
      setGroupList({
        'Filtered': [],
        'All': [] ,
      });
    }
  }, [sortedData]);

  useEffect(()=>{
    if(location.pathname){
      setViewFilter('newest')
      // Set default value to first option when pathname changes
      const options = getDynamicOptions();
      setDynamicDropdownValue(options[0]?.value || '');
    }
  },[location.pathname])


  const handleViewFilterChange = (event: any) => {
    setViewFilter(event.target.value);
    // Set default value to first option when filter changes
    const newOptions = getDynamicOptions(event.target.value);
    setDynamicDropdownValue(newOptions[0]?.value || '');
  };

  const handleDynamicDropdownChange = (event: any) => {
    setDynamicDropdownValue(event.target.value);
  };

  const { selectedObject } = useGlobalSearchStore();
  useEffect(()=>{
    if(selectedObject?.id){
      const isFiltered = grouped?.Filtered?.find((item: any) => item.id === selectedObject.id);
      doNavigateToOrder(selectedObject, -1, false , isFiltered);
      scrollToPurchaseOrder(selectedObject.id)
    }
  }, [selectedObject]);

  useEffect(() => {
    if(orderDetail){
      scrollToPurchaseOrder(orderDetail.id)
    }
  }, [orderDetail])

  const scrollToPurchaseOrder = (poId: string) => {
    const element = itemRefs.current[poId];
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  const doNavigateToOrder = (purchaseOrder, index, showPopup , isFiltered) => {
    setErrorPopupDetail(false)
    setOrderToBeShownInOrderAccept(purchaseOrder);
    const data = { showPopup, index , isFiltered };
    setIsFiltered(isFiltered);
    setIndex(index);
    setShowPopup(showPopup);
  }

  const navigateToAcceptOrder = ($event, purchaseOrder, index, showPopup , isFiltered) => {
    $event?.stopPropagation();
    // setPopupMessage("");
    doNavigateToOrder(purchaseOrder, index, showPopup , isFiltered);
    // navigate(routes.acceptOrderPage, { state: data });
};

  
const handleClickOrder = (e, order, index, showPopup
   , isFiltered) => {
  e.stopPropagation();
  // if(index === selectedOrderIndex) {
  //     setSelectedOrder(null);
  //     setSelectedOrderIndex(null);
  // }
  // else {
  //     setSelectedOrder(order);
  //     setSelectedOrderIndex(index);
  // }

  navigateToAcceptOrder(e, order, index, false , isFiltered)
}

  const handleSettingNavigate = () => {
    navigate(routes.sellerSettingPage, { state: { tab: 'SHIPMENTS' } })
  }

  // MUI Accordion change handler
  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  return (
    <div className={styles.listTab}>
      <div className={styles.createNew}>
      </div>
      <div className={styles.titleSection}>
        <span>
          {location.pathname === routes.previewOrderPage ? <span className={clsx(styles.instantPriceSearch, styles.title)}>PREVIEW ORDERS</span> : 
          location.pathname === routes.deleteOrderPage ? <span className={clsx(styles.deletedItems, styles.title)}>DELETED ITEMS</span> :
           <span className={clsx(styles.quoting, styles.title)}>CLAIM ORDERS</span>}
        </span>
      </div>
      <div className={styles.filterSection}>
        <div className={styles.filterSectionLeft}>
          <Select
            value={viewFilter}
            onChange={handleViewFilterChange}
            open={selectOpen}
            onOpen={() => setSelectOpen(true)}
            onClose={() => setSelectOpen(false)}
            className={'selectDropdown'}
            MenuProps={
              {
                classes: {
                  paper: styles.dropDownBG
                },
              }
            }
          >
            <MenuItem value="newest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Newest</span>
              </div>
            </MenuItem>
            <MenuItem value="oldest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Oldest</span>
              </div>
            </MenuItem>
            <MenuItem value="a-to-z" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>A to Z</span>
              </div>
            </MenuItem>
            <MenuItem value="z-to-a" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Z to A</span>
              </div>
            </MenuItem>
            <MenuItem value="highest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Highest</span>
              </div>
            </MenuItem>
            <MenuItem value="lowest" className={styles.menuItem}>
              <div className={styles.menuItemContent}>
                <span>Lowest</span>
              </div>
            </MenuItem>
          </Select>
        </div>
        {/* Single Dynamic Dropdown */}
        <div className={styles.filterSectionRight}>
          <Select
            value={dynamicDropdownValue}
            onChange={handleDynamicDropdownChange}
            open={dynamicDropdownOpen}
            onOpen={() => setDynamicDropdownOpen(true)}
            onClose={() => setDynamicDropdownOpen(false)}
            className={'selectDropdown'}
            MenuProps={{
              classes: {
                paper: styles.dropDownBG
              },
            }}
          >
            {dynamicOptions.map((option) => (
              <MenuItem 
                key={option.value} 
                value={option.value} 
                className={styles.menuItem}
                disabled={option.disabled}
              >
                <div className={styles.menuItemContent}>
                  <span>{option.label}</span>
                </div>
              </MenuItem>
            ))}
          </Select>
        </div>
      </div>
      <div className={styles.listSection}>
        <div className={styles.savedSearchListContainer}>
        {Object.keys(grouped).length > 0 ?
          Object.entries(grouped).map(([label, items]: any, index: number) => (
            <div key={index} className={clsx(styles.searchContainer, items.length === 0 && styles.noAccordionDetails)}>
              {label === 'All' ? (
                <Accordion 
                  expanded={expanded === 'all-panel'} 
                  onChange={handleAccordionChange('all-panel')}
                  className={clsx(expanded === 'all-panel' && styles.expanded, styles.allAccordion)}
                  sx={{
                    backgroundColor: 'transparent',
                    boxShadow: 'none',
                    '&:before': {
                      display: 'none',
                    },
                    '& .MuiAccordionSummary-root': {
                      backgroundColor: '#303136',
                      height: '40px !important',
                      minHeight: '40px !important',
                      padding: '0 12px',
                      borderBottom: '1px solid #404040',
                      borderTopLeftRadius: '6px',
                      borderTopRightRadius: '6px',
                    },
                    '& .MuiAccordionSummary-content': {
                      margin: 0,
                      fontFamily: 'Syncopate',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      letterSpacing: '0.84px',
                      color: '#fff',
                      textTransform: 'uppercase',
                    },
                    '& .MuiAccordionDetails-root': {
                      padding: 0,
                      height: '100%',
                    },
                    '& .MuiCollapse-vertical': {
                        height: '100% !important',
                    },
                    '& .MuiAccordion-region': {
                      height: '100%',
                    },
                  }}
                >
                  <AccordionSummary>
                    {label} <span className={styles.arrowIcon}><ArrowIcon /></span>
                  </AccordionSummary>
                  <AccordionDetails>
                    <div 
                      className={clsx(styles.searchItemsContainer, 
                      items.length === 0 && styles.noAccordionDetails)}>
                      {items.length > 0 ? (
                        items.map((item: any, index: number) => (
                          <SellerListItemTemplate order={item} key={index} handleClickOrder={handleClickOrder} index={index} label={label} disbaleExportPoBtn={disbaleExportPoBtn} handleExportPDfClick={handleExportPDfClick} itemRefs={itemRefs}/>
                        ))
                      ) : (
                        <div className={styles.noDataContainer}>
                          <span className={styles.noDataMessage}>
                          <>
                                You're caught up!<br />
                                There are no available <br />orders to claim right now.
                              </>
                          </span>
                        </div>
                      )}
                    </div>
                  </AccordionDetails>
                </Accordion>
              ) : (
                <>
                  <p className={styles.searchLabel}>{label}</p>
                  {label === 'Filtered' && <span className={styles.settingsIcon} onClick={handleSettingNavigate}><SettingsIcon /></span>}
                  <div className={clsx(styles.searchItemsContainer, items.length === 0 && styles.noAccordionDetails)}>
                    {items.length > 0 ? (
                      items.map((item: any, index: number) => (
                        <SellerListItemTemplate order={item} key={index} handleClickOrder={handleClickOrder} index={index} label={label} disbaleExportPoBtn={disbaleExportPoBtn} handleExportPDfClick={handleExportPDfClick} itemRefs={itemRefs}/>
                      ))
                    ) : (
                      <div className={styles.noDataContainer}>
                        <span className={styles.noDataMessage}>
                          {label === 'Filtered' 
                            ? (
                              <>
                                You're caught up!<br />
                                There are no available <br />orders to claim right now.
                              </>
                            )
                            : 
                            <>
                                You're caught up!<br />
                                There are no available <br />orders to claim right now.
                              </>
                          }
                        </span>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          ))
          :
          <div className={styles.noDataContainer}>
            <span className={styles.noDataMessage}>No Data Found</span>
          </div>
        }
        </div>
      </div>
    </div>
  )
}

export default ListTab
