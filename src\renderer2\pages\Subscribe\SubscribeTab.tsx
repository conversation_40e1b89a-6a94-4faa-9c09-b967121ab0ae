import React, { useEffect, useRef, useState } from 'react'
import { Dialog, DialogContent, IconButton } from '@mui/material'
import { Close as CloseIcon } from '@mui/icons-material'
import styles from './SubscribeTab.module.scss'
import clsx from 'clsx'
import NextPaymentDetail from './NextPaymentDetail'
import SubscribeUserTable from './SubscribeUserTable'
import UploadUserListDialog from './components/UploadUserListDialog'
import SubscriptionSetup from 'src/renderer2/component/SubscriptionDialog/components/SubscriptionSetup'
import { SubscribeUserTableFormData } from './SubscribeUserTable.schema';
import { useSubscriptionStore } from '@bryzos/giss-ui-library'
import SubscribeEditDialog from './components/SubscribeEditDialog'
import SubscribeSuccessDialog from './components/SubscribeSuccessDialog'
import { useNavigate } from 'react-router-dom'
import { routes } from 'src/renderer2/common'

const SubscribeTab = ({setActiveTab, setSaveFunctions, shipmentListingRef}: {shipmentListingRef: React.RefObject<HTMLDivElement>,setActiveTab: (tab: string) => void, setSaveFunctions: (saveFunctions: any) => void}) => {
    const { setSubscriptionDialogOpen , setIsFromSetting , userSubscription , setUserList , subscriptionDialogOpen } = useSubscriptionStore();
    // Dialog state management
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogContent, setDialogContent] = useState<React.ReactNode>(null);
    const assignableLicensesCount = 3
    const [isUploadUserListOpen, setIsUploadUserListOpen] = useState(false);
    const [isEditLicenseModule, setIsEditlicenseModule] = useState(false);
    const navigate = useNavigate();
    

    useEffect(() => {
        if(userSubscription?.users?.length > 0){
            setUserList(userSubscription?.users)
        }else{
            setUserList([])
        }
    }, [userSubscription])

    const handleBuyLicense = () => {
        setSubscriptionDialogOpen(true);
        setIsFromSetting(true);
    }

    const handleEditPaymentInfo = () => {
    }

    // Dialog handlers for the three buttons
    const handleEditLicenses = () => {
        setDialogContent(<SubscribeEditDialog mode={"EDIT_LICENSES"} closeDialog={handleCloseDialog} onSuccess={handleSuccessDialog}/>);
        setDialogOpen(true);
        setIsEditlicenseModule(true);
    };

    const handleEditPaymentInfoDialog = () => {
        setDialogContent(<SubscribeEditDialog mode={"EDIT_PAYMENT"} closeDialog={handleCloseDialog} onSuccess={handleSuccessDialog}/>);
        setDialogOpen(true);
    };

    const handleUploadUserList = () => {
        setDialogContent(<UploadUserListDialog closeDialog={handleCloseDialog}/>);
        setDialogOpen(true);
        setIsUploadUserListOpen(true);
    };

    const handleCloseDialog = () => {
        setDialogOpen(false);
        setDialogContent(null);
        setIsUploadUserListOpen(false);
        setIsEditlicenseModule(false);
    };

    const handleSuccessDialog = (content: React.ReactNode) => {
        setDialogContent(<SubscribeSuccessDialog content={content}/>);
        setDialogOpen(true);
        setIsEditlicenseModule(false);
    };

    const handleGoToSettings = (redirectTo: string) => {
        navigate(routes.buyerSettingPage, { state: { tab: redirectTo } })
        setSubscriptionDialogOpen(false);
      }
    
    return (
        <div className={clsx(styles.subscribeTab, subscriptionDialogOpen && styles.subscriptionCompleted)}>
            <div className={clsx(styles.subscribeTabHeader, styles.grid)}>
                {
                    subscriptionDialogOpen ? (<>
                    <div style={{width: '100%'}}>
                        <div className={styles.licenseDetailHeader}>
                            <div className={styles.stepTitle}>
                                <span>Step 1: Buy Licenses</span>
                            </div>
                            <div className={styles.licenseCount}>
                                <span>You have</span> {userSubscription?.licenses?.current_total} paid licenses.
                            </div>
                        </div>
                            <div className={styles.licenseDetailContainer}>
                                <div className={clsx(styles.licenseDetailBox,styles.needMoreLicensesBox)}>
                                    <p className={styles.licenseDetailText1}>Need more licenses?</p>
                                    <p className={styles.licenseDetailText2}>You can manage your licenses in settings (top right).</p>
                                    <button className={styles.goToSettingsButton} onClick={() => handleGoToSettings('SUBSCRIPTION')}> Go to settings</button>
                                </div>
                                <div className={clsx(styles.licenseDetailBox, styles.successMessage)}>
                                    <span className={styles.successMessageTitle}>SUCCESS!</span>
                                    <span className={styles.successMessageText}>You have successfully purchased ${userSubscription?.licenses?.current_total || 0} licenses.</span>
                                    <span className={styles.successMessageText1}>Use the table below to assign the license to a User.</span>
                                </div>
                                <div className={styles.licenseDetailBox}>
                                    <NextPaymentDetail />
                                </div>

                            </div>
                       
                    </div>
                    </>) : (
                        <div className={styles.nextPaymentDetailContainer}>
                        <div className={clsx(styles.nextPaymentDetail, styles.equalSpacing, styles.licenseDetailText)}>
                            {
                                userSubscription?.subscription_id ? <NextPaymentDetail /> : "Click 'Buy Licenses' to proceed with your purchase."
                            }
                        </div>
                        <div className={clsx(styles.licenseDetail, styles.equalSpacing)}>

                            <span className={styles.licenseDetailText}>{ `${userSubscription?.licenses?.assigned || 0} OF ${userSubscription?.licenses?.current_total || 0} LICENSES ASSIGNED`}</span>
                        </div>
                        </div>
                    )
                }
            </div>
            <div className={clsx(styles.subscriptionActions, styles.grid)}>
                <div className={clsx(styles.actionbarTitle, styles.equalSpacing)}>
                    <div className={styles.title}>
                        {subscriptionDialogOpen && <span>Step 2: </span>}
                         <span>ADD USERS & ASSIGN LICENSES</span>
                    </div>
                    <p className={styles.description}>After you purchase licenses, you can assign those licenses to users.</p>
                </div>

                {
                    !subscriptionDialogOpen ? (
                <div className={clsx(styles.actionbarButtons, styles.equalSpacing)}>
                    {
                        (userSubscription?.subscription_id)? (
                            <>
                    <button className={styles.actionbarButton} onClick={handleEditLicenses}>
                        <span>Edit Licenses</span>
                    </button>
                    <button className={styles.actionbarButton} onClick={handleEditPaymentInfoDialog}>
                        <span>Edit Payment Info</span>
                    </button>
                    <button className={styles.actionbarButton} onClick={handleUploadUserList} >
                        <span>Upload User List</span>
                    </button>
                            
                            </>
                        ) : (
                            <>
                            <button className={styles.actionbarButton} onClick={handleBuyLicense}>
                        <span>Buy Licenses</span>
                    </button>
                            </>
                        )
                    }
                </div>  
                    ) : (
                        <span className={styles.licenseDetailText}>{ `${userSubscription?.licenses?.assigned || 0} OF ${userSubscription?.licenses?.current_total || 0} LICENSES ASSIGNED`}</span>
                    )
                }
            </div>
            <div className={styles.subscribeUserTable}>
                    <SubscribeUserTable setSaveFunctions={setSaveFunctions} />
            </div>

            <Dialog
                open={dialogOpen}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                disableScrollLock={true}
                container={shipmentListingRef.current}

                classes={
                    {
                        root:clsx(styles.editPaymentInfoPopup, isUploadUserListOpen && styles.uploadUserListPopup , isEditLicenseModule && styles.editLicensePopup),
                        paper:styles.popupContent
                    }
                }
                  style={{
                        position: 'absolute',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backdropFilter: 'blur(7px)',
                        WebkitBackdropFilter: 'blur(7px)',
                        backgroundColor: 'rgba(0, 0, 0, 0.02)',
                        border: '1px solid transparent',
                        borderRadius: '0px 0px 20px 20px',
                        }}
                        PaperProps={{
                        style: {
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            margin: 0
                        }
                        }}
                      hideBackdrop
            >
                <DialogContent sx={{ p: 0, position: 'relative' }}>
                    {dialogContent}
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default SubscribeTab