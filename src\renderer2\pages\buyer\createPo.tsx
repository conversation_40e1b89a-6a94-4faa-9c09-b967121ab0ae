import { <PERSON>complete, Box, ClickAwayListener, Dialog, Fade, IconButton, styled, Tooltip, Typography, TextField } from '@mui/material';
import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router';
import styles from './createPo.module.scss';
import { ReactComponent as QuestionIcon } from '../../assets/images/setting-question.svg';
import { ReactComponent as QuestionHoverIcon } from '../../assets/images/question-white-hover.svg';
import { ReactComponent as QuestionBlackIcon } from '../../assets/images/setting-black-question.svg';
import { ReactComponent as QuestionBlackHoverIcon } from '../../assets/images/question-black-hover.svg';
import { ReactComponent as AddLineIcon } from '../../assets/images/Add-line.svg';
import { ReactComponent as AddLineHoverIcon } from '../../assets/images/Add-line-hover.svg';
import { ReactComponent as RefreshNet30Icon } from '../../assets/images/net30refresh.svg';
import { ReactComponent as DropdownIcon } from '../../assets/New-images/StateIconDropDpown.svg';

import { DeliveryDateTooltip, DeliveryToTooltip, JobPOTooltip, SalesTaxTooltip, chooseYourUnitTooltip } from '../../tooltip';
import clsx from 'clsx';
import { Controller, set, useFieldArray } from 'react-hook-form';
import { CustomMenu } from './CustomMenu';
import { useDebouncedValue } from '@mantine/hooks';
import dayjs from 'dayjs';
import useGetUserPartData from '../../hooks/useGetUserPartData';
import { useQueryClient } from '@tanstack/react-query';
import CreatePoTile from './CreatePoTile/CreatePoTile';
import { ReactComponent as WarningIcon } from '../../assets/images/warning-seller-icon.svg';
import { v4 as uuidv4 } from 'uuid';
import { CommonTooltip } from '../../component/Tooltip/tooltip';
import PdfMakePage from '../PdfMake/pdfMake';
import { InstantPurchasingWrapper,  useBuyerCheckOut, useGlobalStore, useSaveProductSearchAnaytic, useSaveUserActivity, userRole, reactQueryKeys, formatToTwoDecimalPlaces, getChannelWindow, format4DigitAmount, useGetDeliveryDate, mobileDiaglogConst, dateTimeFormat, getFloatRemainder, getValUsingUnitKey, useBuyerSettingStore, useCreatePoStore, uploadBomConst, getSocketConnection, commomKeys, getFormattedUnit, getUnitForOrderIncrement, useBuyerCheckOutNode, orderIncrementPrefix, priceUnits, newPricingPrefix} from '@bryzos/giss-ui-library';
import { calculateBuyerTotalOrderWeight, calculateBuyerTotalOrderWeightForGear, calculateTotalWeightForProduct, descriptionLines, getOtherDescriptionLines } from '../../utility/pdfUtils';
import SearchHeader from '../SearchHeader';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import { ReactComponent as UploadBOMIcon } from '../../assets/New-images/Upload-BOM.svg';
import { ReactComponent as UploadBOMIconHover } from '../../assets/New-images/Bom-Upload-Hover.svg';
import { ReactComponent as ChooseOneIcon } from '../../assets/New-images/Choose-One.svg';
import OrderSummary from 'src/renderer2/component/OrderSummary/OrderSummary';
import { useRightWindowStore } from '../RightWindow/RightWindowStore';
import StatesList from 'src/renderer2/component/StatesList/StatesList';
import { ChevronLeft, ChevronRight, Tab } from '@mui/icons-material';
import { ArrowLeftIcon, ArrowRightIcon } from '@mui/x-date-pickers';
import StateDropDown from 'src/renderer2/component/StateDropDown/StateDropDown';
import UploadReviewWindow from 'src/renderer2/component/UploadReviewWindow/UploadReviewWindow';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { navigationConfirmMessages, routes, BNPLSTATUS, bomLineStatusCountObjDefault, localStorageKeys } from 'src/renderer2/common';
import PseudoScroll from 'src/renderer2/component/PseudoScroller/PseudoScroll';
import BomTile from './bomTile/BomTile';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import UploadBomSummary from 'src/renderer2/component/UploadBomSummary/UploadBomSummary';
import BomProcessingWindow from 'src/renderer2/component/BomProcessingWindow/BomProcessingWindow';
import useSaveBomHeaderDetails from 'src/renderer2/hooks/useSaveBomHeaderDetails';
import SavedBomPreview from 'src/renderer2/component/SavedBomPreview/SavedBomPreview';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import usePostDraftPo from 'src/renderer2/hooks/usePostDraftPo';
import { useBomPdfExtractorStore } from './BomPdfExtractor/BomPdfExtractorStore';
import { getPriceExample } from 'src/renderer2/utility/priceIntegratorExample';
import useMutateGetDeliveryAddress from 'src/renderer2/hooks/useMutateGetDeliveryAddress';

let cartItem = {
    descriptionObj: '',
    description: '',
    qty: '',
    qty_unit: '',
    price_unit: '',
    product_tag: '',
    domestic_material_only: false,
    qtyUnitM:[],
    priceUnitM:[],
    line_session_id:'',
}

const _bomProductMappingSocketData = {
    "id": "9b2e0005-4b20-41c7-b426-c7f3a19cfeaf",
    "file_name": "Summary List - Zone 4 Main Members 2.pdf",
    "actual_file_name": "Summary List - Zone 4 Main Members 2.pdf",
    "unique_identifier": "20250402063142",
    "status": "COMPLETED",
    "s3_url": "https://extended-widget-uploads.s3.amazonaws.com/staging/bom/Summary List - Zone 4 Main Members 2-435bd4c6-1dd5-4090-b254-0ab6c9ac3d54.pdf",
    "total_pages": 14,
    "result": [
        {
            "id": "583bb2d4-c6fa-4f46-9992-903526293f49",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 16 x 67",
            "specification": null,
            "search_string": "w 16x67 a992 (length 660 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "55'- 0",
            "weight_per_quantity": "3685.00",
            "matched_product_count": 1,
            "matched_products": "[347045]",
            "selected_products": "[347045]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 2
        },
        {
            "id": "f967ea60-4858-47f8-82dc-80827b43cff8",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 50",
            "specification": null,
            "search_string": "w 16x50 a992 (weight 1500.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "1500.00",
            "matched_product_count": 1,
            "matched_products": "[345035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 3
        },
        {
            "id": "c84246ed-03fd-4de4-b7ee-4aebdeef2861",
            "status": "APPROVED",
            "confidence": 0,
            "product_tag": null,
            "description": "w 16 x 40",
            "specification": null,
            "search_string": "w 16x40 a992 (weight 800.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "20'- 0",
            "weight_per_quantity": "800.00",
            "matched_product_count": 1,
            "matched_products": "[343030]",
            "selected_products": "[343020]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 4
        },
        {
            "id": "e914705b-5a7e-44c4-bc17-f29530fcc1ae",
            "status": "APPROVED",
            "confidence": 0,
            "product_tag": null,
            "description": "w 16 x 36",
            "specification": "test",
            "search_string": "w 16x36 a992 (length 420 inches was considered)",
            "grade": "A992",
            "qty": "2",
            "qty_unit": "Ea",
            "length": "35'- 0",
            "weight_per_quantity": "1260.00",
            "matched_product_count": 2,
            "matched_products": "[85010,342040,342035]",
            "selected_products": "[85010,342040]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 5
        },
        {
            "id": "e1f0fe29-90df-4ed6-adf9-b0cbec131f8d",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (weight 1240.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "40'- 0",
            "weight_per_quantity": "1240.00",
            "matched_product_count": 1,
            "matched_products": "[341040]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 6
        },
        {
            "id": "8616561a-a731-444c-9d2b-fec39ace9dbd",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (length 540 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "45'- 0",
            "weight_per_quantity": "1395.00",
            "matched_product_count": 2,
            "matched_products": "[341040,341045]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 7
        },
        {
            "id": "d33fa086-2e6b-495c-a46d-1211dc0bcf68",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (weight 1550.00 lb was considered)",
            "grade": "A992",
            "qty": "3",
            "qty_unit": "Ea",
            "length": "50'- 0",
            "weight_per_quantity": "1550.00",
            "matched_product_count": 1,
            "matched_products": "[341045]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 8
        },
        {
            "id": "82c4a162-9e89-48b9-904c-0d4a328c38b5",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 12 x 22",
            "specification": null,
            "search_string": "w 12x22 a992 (length 300 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "25'- 0",
            "weight_per_quantity": "550.00",
            "matched_product_count": 3,
            "matched_products": "[292030,292035,292045]",
            "selected_products": "[]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 9
        },
        {
            "id": "47b20b8b-32eb-44cb-977b-e97a52157ae1",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 12 x 22",
            "specification": null,
            "search_string": "w 12x22 a992 (weight 880.00 lb was considered)",
            "grade": "A992",
            "qty": "2",
            "qty_unit": "Ea",
            "length": "40'- 0",
            "weight_per_quantity": "880.00",
            "matched_product_count": 1,
            "matched_products": "[292040]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 10
        },
        {
            "id": "71dd75cb-66ef-4434-8bb4-153174936e83",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 10 x 49",
            "specification": null,
            "search_string": "w 10x49 a992 (weight 1470.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "1470.00",
            "matched_product_count": 1,
            "matched_products": "[281035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 11
        },
        {
            "id": "332d2014-effb-4aec-9c42-0f6564d55806",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 10 x 49",
            "specification": null,
            "search_string": "w 10x49 a992 (length 540 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "45'- 0",
            "weight_per_quantity": "2205.00",
            "matched_product_count": 2,
            "matched_products": "[281040,281045]",
            "selected_products": "[281045]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 12
        },
        {
            "id": "625572b1-e97d-4a21-b945-a225aa4ba4b9",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 10 x 19",
            "specification": null,
            "search_string": "w 10x19 a992 (weight 570.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "570.00",
            "matched_product_count": 1,
            "matched_products": "[274035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 13
        },
        {
            "id": "02f123de-ed08-4e8a-a693-4f82ed2b027f",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 10 x 12",
            "specification": null,
            "search_string": "w 10x12 a992 (length 300 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "25'- 0",
            "weight_per_quantity": "300.00",
            "matched_product_count": 3,
            "matched_products": "[271030,271035,271045]",
            "selected_products": "[271035]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 14
        },
        {
            "id": "44932d5d-00f3-4db0-b759-74eab54dc7ad",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 8 x 10",
            "specification": null,
            "search_string": "w 8x10 a992 (weight 200.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "20'- 0",
            "weight_per_quantity": "200.00",
            "matched_product_count": 1,
            "matched_products": "[258030]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 15
        },
        {
            "id": "583bb2d4-c6fa-4f46-9992-903526293f49",
            "status": "PENDING",
            "confidence": 50,
            "product_tag": null,
            "description": "",
            "specification": null,
            "search_string": "w 16x67 a992 (length 660 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "55'- 0",
            "weight_per_quantity": "3685.00",
            "matched_product_count": 1,
            "matched_products": "[347045]",
            "selected_products": "[347045]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 2
        },
        {
            "id": "f967ea60-4858-47f8-82dc-80827b43cff8",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 50",
            "specification": null,
            "search_string": "w 16x50 a992 (weight 1500.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "1500.00",
            "matched_product_count": 1,
            "matched_products": "[345035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 3
        },
        {
            "id": "c84246ed-03fd-4de4-b7ee-4aebdeef2861",
            "status": "PENDING",
            "confidence": 0,
            "product_tag": null,
            "description": "w 16 x 40",
            "specification": null,
            "search_string": "w 16x40 a992 (weight 800.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "20'- 0",
            "weight_per_quantity": "800.00",
            "matched_product_count": 1,
            "matched_products": "[343030]",
            "selected_products": "[343020]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 4
        },
        {
            "id": "e914705b-5a7e-44c4-bc17-f29530fcc1ae",
            "status": "PENDING",
            "confidence": 0,
            "product_tag": null,
            "description": "w 16 x 36",
            "specification": "test",
            "search_string": "w 16x36 a992 (length 420 inches was considered)",
            "grade": "A992",
            "qty": "2",
            "qty_unit": "Ea",
            "length": "35'- 0",
            "weight_per_quantity": "1260.00",
            "matched_product_count": 2,
            "matched_products": "[85010,342040,342035]",
            "selected_products": "[85010,342040]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 5
        },
        {
            "id": "e1f0fe29-90df-4ed6-adf9-b0cbec131f8d",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (weight 1240.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "40'- 0",
            "weight_per_quantity": "1240.00",
            "matched_product_count": 1,
            "matched_products": "[341040]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 6
        },
        {
            "id": "8616561a-a731-444c-9d2b-fec39ace9dbd",
            "status": "PENDING",
            "confidence": 50,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (length 540 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "45'- 0",
            "weight_per_quantity": "1395.00",
            "matched_product_count": 2,
            "matched_products": "[341040,341045]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 7
        },
        {
            "id": "d33fa086-2e6b-495c-a46d-1211dc0bcf68",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 16 x 31",
            "specification": null,
            "search_string": "w 16x31 a992 (weight 1550.00 lb was considered)",
            "grade": "A992",
            "qty": "3",
            "qty_unit": "Ea",
            "length": "50'- 0",
            "weight_per_quantity": "1550.00",
            "matched_product_count": 1,
            "matched_products": "[341045]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 8
        },
        {
            "id": "82c4a162-9e89-48b9-904c-0d4a328c38b5",
            "status": "PENDING",
            "confidence": 50,
            "product_tag": null,
            "description": "w 12 x 22",
            "specification": null,
            "search_string": "w 12x22 a992 (length 300 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "25'- 0",
            "weight_per_quantity": "550.00",
            "matched_product_count": 3,
            "matched_products": "[292030,292035,292045]",
            "selected_products": "[]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 9
        },
        {
            "id": "47b20b8b-32eb-44cb-977b-e97a52157ae1",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 12 x 22",
            "specification": null,
            "search_string": "w 12x22 a992 (weight 880.00 lb was considered)",
            "grade": "A992",
            "qty": "2",
            "qty_unit": "Ea",
            "length": "40'- 0",
            "weight_per_quantity": "880.00",
            "matched_product_count": 1,
            "matched_products": "[292040]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 10
        },
        {
            "id": "71dd75cb-66ef-4434-8bb4-153174936e83",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 10 x 49",
            "specification": null,
            "search_string": "w 10x49 a992 (weight 1470.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "1470.00",
            "matched_product_count": 1,
            "matched_products": "[281035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 11
        },
        {
            "id": "332d2014-effb-4aec-9c42-0f6564d55806",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 10 x 49",
            "specification": null,
            "search_string": "w 10x49 a992 (length 540 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "45'- 0",
            "weight_per_quantity": "2205.00",
            "matched_product_count": 2,
            "matched_products": "[281040,281045]",
            "selected_products": "[281045]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 12
        },
        {
            "id": "625572b1-e97d-4a21-b945-a225aa4ba4b9",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 10 x 19",
            "specification": null,
            "search_string": "w 10x19 a992 (weight 570.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "30'- 0",
            "weight_per_quantity": "570.00",
            "matched_product_count": 1,
            "matched_products": "[274035]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 13
        },
        {
            "id": "02f123de-ed08-4e8a-a693-4f82ed2b027f",
            "status": "APPROVED",
            "confidence": 50,
            "product_tag": null,
            "description": "w 10 x 12",
            "specification": null,
            "search_string": "w 10x12 a992 (length 300 inches was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "25'- 0",
            "weight_per_quantity": "300.00",
            "matched_product_count": 3,
            "matched_products": "[271030,271035,271045]",
            "selected_products": "[271035]",
            "current_page": 1,
            "total_pages": 1,
            "product_index": 14
        },
        {
            "id": "44932d5d-00f3-4db0-b759-74eab54dc7ad",
            "status": "APPROVED",
            "confidence": 100,
            "product_tag": null,
            "description": "w 8 x 10",
            "specification": null,
            "search_string": "w 8x10 a992 (weight 200.00 lb was considered)",
            "grade": "A992",
            "qty": "1",
            "qty_unit": "Ea",
            "length": "20'- 0",
            "weight_per_quantity": "200.00",
            "matched_product_count": 1,
            "matched_products": "[258030]",
            "selected_products": null,
            "current_page": 1,
            "total_pages": 1,
            "product_index": 15
        }
    ]
};

const InstantPurchasing = (
    {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        watch,
        errors,
        isValid,
        reset,
        handleStateZipValidation,
        quantitySizeValidator,
        calculateMaterialTotalPrice,
        buyingPreference,
        setBuyingPreference,
        calculateTotalPricing,
        handleSalesTax,
        paymentMethodChangeHandler,
        saveCreatePOUserActivity,
        saveUserLineActivity,
        setDefaultPaymentOption,
        paymentMethods,
        clearErrors,
        navigateToSettings,
        trigger,
        setError
    }
) => {
    const navigate = useNavigate();
    const location = useLocation();
    // const isCreatePOModule = true; //location.pathname === routes.createPoPage;
    
    const {setShowLoader, backNavigation, setCreatePoSessionId, resetHeaderConfig, userData, setBackNavigation, bryzosPayApprovalStatus, setBryzosPayApprovalStatus, referenceData, productData, productMapping , discountData, referenceDataUpdated} = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setIsCreatePoDirty, isCreatePoDirty, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom , setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom , setBomDataIdToRefresh, bomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData} = useCreatePoStore();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setOpenLeftPanel, setDisplayLeftPanel } = useLeftPanelStore();
    
    const {bomData, setPdfUrl, setBomUploadID, setPdfFileName} = useBomPdfExtractorStore();
    // Add the navigation interception
    const handleConfirmNavigation = (to, options, message) => {
        showCommonDialog(
            null, 
            message, 
            null, 
            resetDialogStore, 
            [
                {
                    name: 'Yes', 
                    action: () => handleDialogYesBtn(to, options)
                }, 
                {
                    name: 'No', 
                    action: resetDialogStore
                }
            ]
        );
    };

    const handleDialogYesBtn = (to, options) => {
        handleLeavePageAction();
        setIsCreatePoDirty(false)
        navigate(to, options);
        resetDialogStore();
    }

    const navigateWithConfirmation = (shouldConfirm: boolean = false, to: string, options?: any, message?: string ) => {
        const targetRoute = to ? to : location.pathname;
      if (shouldConfirm) {
        handleConfirmNavigation(targetRoute, options, message);
        return;
      }
      // If no confirmation needed, just navigate
      navigate(targetRoute, options);
    };

    const channelWindow =  getChannelWindow();
    const [products, setProducts] = useState([]);
    const [states, setStates] = useState([]);
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [showLine1Tooltip, setShowLine1Tooltip] = useState(true);
    const [showCityTooltip, setShowCityTooltip] = useState(true);
    const [showPaymentOptionTooltip, setShowPaymentOptionTooltip] = useState(undefined);
    
    const [openErrorDialog, setOpenErrorDialog] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [sendInvoicesToEmailData, SetSendInvoicesToEmailData] = useState('');
    const [apiCallInProgress, setApiCallInProgress] = useState(false);

    const queryClient = useQueryClient();
    const { data: userPartData, isLoading: isUserPartDataLoading } = useGetUserPartData();
    const logUserActivity = useSaveUserActivity();
    const mutateSaveBomHeaderDetails = useSaveBomHeaderDetails();
    const [viewIndex, setViewIndex] = useState(0);

    const { fields, append, remove, } = useFieldArray({
        control,
        name: "cart_items"
    });

    const [debouncedMatTotal] = useDebouncedValue(watch('price'), 400);
    const [handleSubmitValidation, setHandleSubmitValidation] = useState(false);
    const [sessionId, setSessionId] = useState('');
    const [lineSessionId, setLineSessionId] = useState('');
    const [disableCloseAnalytic, setDisableCloseAnalytic] = useState(true);
    const [deliveryStateFocusEnable, setDeliveryStateFocusEnable] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [searchStringData, setSearchString] = useState('');
    const [todayDate, setTodayDate] = useState('');
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(false);
    const [deliveryDateMap, setDeliveryDateMap] = useState({});
    const [deboucedSearchString, cancelDebouncedSearchString] = useDebouncedValue(searchStringData, 400);
    const analyticRef = useRef();
    analyticRef.current = disableCloseAnalytic && !resetHeaderConfig;
    // const getBuyingPreference = ueGetBuyingPreference();
    const { buyerSetting } = useBuyerSettingStore();
    const saveBuyerCheckout = useBuyerCheckOutNode();
    const saveProductSearch = useSaveProductSearchAnaytic();
    const getDeliveryDate = useGetDeliveryDate();
    const [openDeliveryToDialog, setOpenDeliveryToDialog] = useState(false);
    const [selected, setSelected] = useState("null");
    const [isFocused, setIsFocused] = useState(false);
    const [autocompleteOpen, setAutocompleteOpen] = useState(false);
    const [autocompleteOpenLine2, setAutocompleteOpenLine2] = useState(false);
    const [autocompleteOpenCity, setAutocompleteOpenCity] = useState(false);
    const containerRef = useRef(null);
    const { setLoadComponent , setShowVideo, setProps, bomProcessingWindowProps, setShowBomProcessing, setBOMLineStatusCountObj, bomLineStatusCountObj, setScrollToBomLine, scrollToBomLine} = useRightWindowStore();

    const [stateInputFocus, setStateInputFocus] = useState(false);
    const [stateDropDownValue, setStateDropDownValue] = useState<any>('');
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const [showHeader, setShowHeader] = useState(false);
    const createPoContainerRef = useRef(null);
    const headerContainerRef = useRef<HTMLDivElement>(null);
    const [isNearBottom, setIsNearBottom] = useState(false);
    const [isCartValid, setIsCartValid] = useState(true);
    const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    const [pricingBrackets, setPricingBrackets] = useState([]);
    const [disableBidBuyNow, setDisableBidBuyNow] = useState(false);
    const addPoLineTableRef = useRef(null);
    const[hidePoLineScroll, setHidePoLineScroll] = useState(true);
    const [scrollPosition, setScrollPosition] = useState(0);
    const [maxScrollHeight, setMaxScrollHeight] = useState(800);
    const formInputGroupRef = useRef(null);
    const fileInputRef = useRef(null); // Add ref for file input
    const [isDraggingScrollState, setIsDraggingScrollState] = useState(false);
    const [bomUploadResult, setBomUploadResult] = useState([]);
    const [startIndex, setStartIndex] = useState(0);
    const [prevStartIndex, setPrevStartIndex] = useState(0);
    const [endIndex, setEndIndex] = useState(10);
    const [scrollThumbHeight, setScrollThumbHeight] = useState(0);
    const [isSavedBom, setIsSavedBom] = useState(location.pathname === routes.savedBom);
    const [initialData, setInitialData] = useState(location.pathname !== routes.savedBom ? createPoData : null);
    const [cameFromSavedBom, setCameFromSavedBom] = useState(false);
    const [isHeaderDetailsConfirmed, setIsHeaderDetailsConfirmed] = useState(false);
    const [lastModifiedBom, setLastModifiedBom]= useState<string | null>(null);
    const [filteredFields, setFilteredFields] = useState<any>([]);
    const lastModifiedBomRef = useRef(null);
    const jobPoInputRef = useRef(null);
    const [currentBomData, setCurrentBomData] = useState(location.pathname === routes.savedBom ? bomProductMappingSocketData : null);
    const postDraftPo = usePostDraftPo();
    // Track the cart items length separately
    const cartItemsLength = watch('cart_items')?.length || 0;
    const socket = getSocketConnection();
    let viewLineStatusIndex = startIndex;
    const {mutateAsync: getDeliveryAddresses, data: deliveryAddressData} = useMutateGetDeliveryAddress();
    const HeaderDetailsConfirmedRef = useRef(null);
    const {leftPanelData , setLeftPanelData} = useLeftPanelStore();
    const [debouncedTotalWeight] = useDebouncedValue(watch('totalWeight'), 400);
    const [disableReviewCompleteButton, setDisableReviewCompleteButton] = useState(false);

    const orderInfoIsFilled = useMemo(()=>{
        return !!watch('shipping_details.line1') && 
               !!watch('shipping_details.city') && 
               !!watch('shipping_details.state_id') && 
               !!watch('shipping_details.zip') &&
               !!watch('internal_po_number') &&
               !!watch('order_type') &&
               !!watch('delivery_date') &&
               !errors?.shipping_details?.zip?.message
    },[
        watch('shipping_details.line1'), 
        watch('shipping_details.city'), 
        watch('shipping_details.state_id'), 
        watch('shipping_details.zip'),
        watch('internal_po_number'),
        watch('order_type'), 
        watch('delivery_date'),
        errors?.shipping_details?.zip?.message
    ]);

    useEffect(()=>{
        setIsSavedBom(location.pathname === routes.savedBom)
        if(!isCreatePOModule){
            //setBomProductMappingSocketData(_bomProductMappingSocketData);
        }
        return(()=>{
            // handleLeavePageAction();
        })
    },[])

    // Replace the existing handleLeavePageAction function
    const handleLeavePageAction = () => {
        const {bomDataIdToRefresh} = useCreatePoStore.getState();
        if(bomDataIdToRefresh){
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }

        if(!isCreatePOModule){
            saveBomHeaderDetails();
        }  
        setBomUploadResult([]);
        if(bomProcessingWindowProps?.isProcessing !== true){
            setBomProductMappingSocketData(null);//do this conditionally
            setIsCreatePOModule(true);
        }
        setScrollToBomLine(null)
        setCreatePoDataFromSavedBom(null);
        setBomProductMappingDataFromSavedBom(null);
        setBomDataIdToRefresh(null);
    }
    
    // Replace the handleNavigateAway function
    const handleNavigateAway = (targetRoute) => {
        const {bomDataIdToRefresh} = useCreatePoStore.getState();
        if(bomDataIdToRefresh){
            socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
        }
        if(location.pathname === routes.savedBom){
            setCreatePoDataFromSavedBom(null);
            setBomProductMappingDataFromSavedBom(null);
            setBomProductMappingSocketData(null);
        }else{
            const isDirty = isCreatePoDirty || !isCreatePOModule;
            const message = isCreatePoDirty
              ? navigationConfirmMessages.unsavedChanges
              :
              navigationConfirmMessages.confirmLeave;
            navigateWithConfirmation(isDirty, targetRoute, undefined, message);
        }
        setBomDataIdToRefresh(null);
    };

    // Use the tracked length in useEffect
    useEffect(() => {
        const formInputGroup = formInputGroupRef.current;
        const addLineContainer = addPoLineTableRef.current;
        if(!!formInputGroup && !!addLineContainer){
            setTimeout(()=>{
                setMaxScrollHeight((formInputGroup?.scrollHeight + addLineContainer?.scrollHeight)??800);
                getScrollThumbHeight(addPoLineTableRef.current);
            },200)
        }
    }, [cartItemsLength, endIndex, filteredFields.length]); // Now depends on the tracked length

    const disableFormValidation = !orderInfoIsFilled ||
        Object.keys(errors).length > 0 ||
        !handleSubmitValidation ||
        watch('shipping_details.validating_state_id_zip') ||
        watch('totalWeight') < pricingBrackets?.[0]?.min_weight ||
        !isCartValid ||
        !watch('totalPurchase');

    // Disable place order button if any validation fails
    const disablePlaceOrderButton = disableFormValidation ||  (watch('payment_method') === 'bryzos_pay' && (watch('bnplStatus') === BNPLSTATUS.ON_HOLD || (watch('bnplStatus') === BNPLSTATUS.RESTRICTED && Number(watch(`totalPurchase`)) > Number(watch('max_restricted_amount')))) ) ||
        !watch('payment_method') ||
        (getValues('payment_method') === 'bryzos_pay' && (
            buyingPreference?.bnpl_settings?.is_approved === null ||
            watch('totalPurchase') > (watch('availableCreditLimit') || 0)
        ));
        
    const checkAtLeastOneApproved = watch('cart_items')?.some((item: any) => item.lineStatus === 'APPROVED')
    const isValidBomForm =  watch('cart_items')?.some((item: any , index: number) => {
        if (item.lineStatus === 'SKIPPED' || item.lineStatus === 'DELETED') {
            return false;
        } else if (item.lineStatus === 'APPROVED') {
            return !(item.qty?.length > 0 && item.descriptionObj && Object.keys(item.descriptionObj).length > 0)
        }
        return (
            item.lineStatus === 'PENDING'
        );
    });

    useEffect(()=>{
        setDisableReviewCompleteButton(!orderInfoIsFilled ||
            Object.keys(errors).length > 0 ||
            !watch('cart_items')?.length ||
            !checkAtLeastOneApproved || isValidBomForm);
    },[orderInfoIsFilled , errors , watch('cart_items') , checkAtLeastOneApproved , isValidBomForm])

    useEffect(() => {
        
        function handleClickOutside(event) {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsFocused(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    useEffect(() => {
        const orderSummaryProps = {
            watch,
            paymentMethods,
            control,
            setValue,
            getValues,
            navigateToSettings,
            saveUserActivity,
            disablePlaceOrderButton,
            handleSubmit,
            onSubmit,
            pricingBrackets,
            isCreatePOModule,
            disableReviewCompleteButton
        };
        const uploadBomSummaryProps = { getValues, disableReviewCompleteButton,  bomProductMappingSocketData: currentBomData, watch }
        setProps(isCreatePOModule ? orderSummaryProps : uploadBomSummaryProps);

    }, [
        watch('price'),
        watch('sales_tax'),
        watch('depositAmount'), 
        watch('subscriptionAmount'),
        watch('totalPurchase'),
        watch('availableCreditLimit'),
        watch('payment_method'),
        watch('requestedCreditLimit'),
        disablePlaceOrderButton,
        isCreatePOModule,
        disableReviewCompleteButton,
        currentBomData
    ]);
    
    useEffect(() => {
        const component = isCreatePOModule ? <UploadReviewWindow /> : <UploadBomSummary />;
        setLoadComponent(component);
    }, [isCreatePOModule]);

    useEffect(() => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200, behavior: 'smooth' });
        };

        let _lastModifiedBom = null;
        if (currentBomData?.id && !isCreatePOModule) {
            _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
            setLastModifiedBom(_lastModifiedBom?.lineId);
        }

        if (
            (_lastModifiedBom || lastModifiedBomRef?.current) &&
            currentBomData &&
            !isCreatePOModule &&
            createPoContainerRef?.current &&
            addPoLineTableRef?.current 
        ) {
            if (_lastModifiedBom !== "HEADER") {
                setTimeout(()=>{
                const container = createPoContainerRef.current;
                const table = addPoLineTableRef.current;
                const element = lastModifiedBomRef.current;
                const localLastModifiedBom = document.getElementById(_lastModifiedBom?.lineId);
                if (container) {
                    container.scrollTo({ top: 300, behavior: 'smooth' });
                }

                if (table && (element || localLastModifiedBom)) {
                    if (localLastModifiedBom) {
                        scrollToElement(table, localLastModifiedBom);
                        focusChildElement(localLastModifiedBom, _lastModifiedBom?.input);
                    } else {
                        scrollToElement(table, element);
                        focusChildElement(element, _lastModifiedBom?.input);
                    }
                    }
                
            },200)
            }else{
                setTimeout(()=>{
                    jobPoInputRef?.current?.focus();
                },100)
            }
        }
    }, [currentBomData]);

    useEffect(() => {
        if(bomProcessingWindowProps?.isProcessing === true){
            setShowVideo(false);
            setShowBomProcessing(true);
        }else{
            setShowVideo(true);
            setShowBomProcessing(false);
        }
        reset();
        setIsAllCartDataLoaded(false);
        setBomUploadResult([])
        setStartIndex(0);
        setPrevStartIndex(0);
        setEndIndex(10);
        setTimeout(() => {
            _init();
        }, 0);
        return(() => {

            setLoadComponent(null)
            setShowBomProcessing(false);
            if(bomProcessingWindowProps?.isProcessing === true){
                setLoadComponent(
                    <div >
                        <BomProcessingWindow 
                            // isCompleted={processingCompleted}
                            // gameScoreData={getGameScore}
                        />
                    </div>
                );
            }
            setBackNavigation(-1)
            cancelDebouncedSearchString()
            if(analyticRef.current  && sessionId && isCreatePOModule){
                const payload = {
                    "data": {
                        "session_id": sessionId,
                        "close_status": "CANCEL"
                    }
                }
                logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload})
                .then(() => setCreatePoSessionId(null))
                .catch(err => console.error(err))
            }
        })
    }, []);

    const _init = ()=>{
        if(isCreatePOModule){
        const cart_items = Array(3).fill({...cartItem});
        setValue('cart_items', cart_items);
    }
    if (productData && referenceData) {
        setProducts(productData)
        setStates(referenceData?.ref_states);
        setPricingBrackets(referenceData?.ref_weight_price_brackets);
        initializeCreatePOData();
    }
    const sessionId = uuidv4();
    setSessionId(sessionId);
    setCreatePoSessionId(sessionId)
    if(isCreatePOModule){
        const payload = {
            "data": {
                "session_id": sessionId
            }
        }
        logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload}).catch(err => console.error(err));
    }
    }

    useEffect(()=>{
        if(currentBomData?.items?.length > 0){
            setStartIndex(0);
            setPrevStartIndex(0);
            setEndIndex(10);
            const cartItem = [];
            const statusCountObj = {...bomLineStatusCountObjDefault};
            for(let i = 0; i < currentBomData.items.length; i++){
                const productObj = {
                    lineStatusIndex: i,
                    bom_line_id: currentBomData.items[i].id || i,
                    lineStatus: currentBomData.items[i].status,
                    originalStatus: currentBomData.items[i].original_line_status || currentBomData.items[i].status,
                    confidence: currentBomData.items[i].confidence,
                    product_tag: currentBomData.items[i].product_tag,
                    description: currentBomData.items[i].description,
                    specification: currentBomData.items[i].specification,
                    search_string: currentBomData.items[i].search_string,
                    matched_products: currentBomData.items[i].matched_products,
                    selected_products: currentBomData.items[i].selected_products,
                    current_page: currentBomData.items[i].current_page,
                    total_pages: currentBomData.items[i].total_pages,
                    product_index: currentBomData.items[i].product_index,
                    grade: currentBomData.items[i].grade,
                    qty: currentBomData.items[i].qty?.replace(/[\$,]/g, '') || currentBomData.items[i].qty,
                    qty_unit: currentBomData.items[i].qty_unit?.toLowerCase(),
                    length: currentBomData.items[i].length,
                    weight_per_quantity: currentBomData.items[i].weight_per_quantity,
                    matched_product_count: currentBomData.items[i].matched_product_count,
                    last_updated_product: currentBomData.items[i]?.last_updated_product ?? 0,
                    domesticMaterialOnly: currentBomData.items[i]?.domestic_material_only || false,
                    pdf_string:currentBomData.items[i]?.pdf_string || '',
                    split_string:currentBomData.items[i]?.split_string || ''

                }
                if(productObj.lineStatus === uploadBomConst.lineItemStatus.approved){
                    statusCountObj[uploadBomConst.lineItemStatus.approved]++;
                }else if(productObj.lineStatus === uploadBomConst.lineItemStatus.pending){
                    statusCountObj[uploadBomConst.lineItemStatus.pending]++;
                }else if(productObj.lineStatus === uploadBomConst.lineItemStatus.skipped){
                    statusCountObj[uploadBomConst.lineItemStatus.skipped]++;
                }else if(productObj.lineStatus === uploadBomConst.lineItemStatus.deleted){
                    statusCountObj[uploadBomConst.lineItemStatus.deleted]++;
                }
                cartItem[i] = productObj;
            }
            setBomUploadResult(cartItem);
            setBOMLineStatusCountObj(statusCountObj);
        }
    },[currentBomData])

    useEffect(() => {
        if(location.pathname !== routes.savedBom){
            if (bomProductMappingSocketData) {
                setCurrentBomData(bomProductMappingSocketData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomProductMappingSocketData])

    useEffect(() => {
        if(location.pathname !== routes.savedBom){
            if (bomData) {
                setCurrentBomData(bomData);
            } else {
                setCurrentBomData(null);
            }
        }
    }, [bomData])

    useEffect(()=>{
        if(bomUploadResult?.length > 0 && !isCreatePOModule){
            setValue('cart_items', []);
            for(let i = 0; i < bomUploadResult.length; i++){
                setDisableBidBuyNow(true);
                append({...bomUploadResult[i]})
                initializeBomTileData(i)
                validateSavedBomCreatePo(i)
            }
            setEndIndex(10);
        }
    },[bomUploadResult])

    useEffect(()=>{
        if(!isCreatePOModule && watch('cart_items')?.length > 0){
            handleFilterFieldsData();
        }
    },[watch('cart_items')])

    useEffect(() => {
        setProducts(productData);
    }, [productData]);
    
    useEffect(() => {
        if (isAllCartDataLoaded && isCreatePOModule && !isSavedBom && !createPoDataFromSavedBom) {
            for(let i = 0; i < 17; i++){
                append({...cartItem});
            }
            setEndIndex(endIndex + 17);
        }
    }, [isAllCartDataLoaded])


    useEffect(()=>{
        const initializeData = async()=>{
            if(initialData && location.pathname === routes.savedBom){
                await initializeCreatePOData();
                setShowLoader(false);
            }
        }
        initializeData();
    },[initialData, location.pathname])

    useEffect(()=>{
        const initializeData = async () => {
            if (isHeaderDetailsConfirmed) {
              await initializeCreatePOData();
            }
          };
    
          initializeData();
    },[isHeaderDetailsConfirmed])

    useEffect(()=>{
        if(location.pathname === routes.savedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)){
            reset();
            setViewIndex(0)
            setBomSummaryViewFilter('all')
            setInitialData(null);
            setBomProductMappingSocketData(null);
            setScrollToBomLine(null)
            if(bomProductMappingDataFromSavedBom){
                console.log('bomProductMappingDataFromSavedBom: ', bomProductMappingDataFromSavedBom);
                setPdfFileName(bomProductMappingDataFromSavedBom?.actual_file_name);    
                setPdfUrl(bomProductMappingDataFromSavedBom?.s3_url);
                setBomUploadID(bomProductMappingDataFromSavedBom?.id);
                setInitialData({...bomProductMappingDataFromSavedBom});
                setCurrentBomData({...bomProductMappingDataFromSavedBom});
                let formattedUploadBomHeaderData = {
                    delivery_date: bomProductMappingDataFromSavedBom?.delivery_date ,
                    shipping_details: bomProductMappingDataFromSavedBom?.shipping_details,
                    order_type: bomProductMappingDataFromSavedBom?.type,
                    internal_po_number: bomProductMappingDataFromSavedBom?.title,
                    bom_id: bomProductMappingDataFromSavedBom?.id,
                }
                setUploadBomInitialData({...formattedUploadBomHeaderData})
            }else if(createPoDataFromSavedBom){
                handleCreatePoDataFromSavedBom();
            }
        }
    },[location.pathname,createPoDataFromSavedBom,bomProductMappingDataFromSavedBom])

    useEffect(()=>{
        if(!(isSavedBom && createPoDataFromSavedBom)){
            setHandleSubmitValidation(false);
            paymentMethodChangeHandler().then(()=>{
                calculateTotalPrice();
                setHandleSubmitValidation(true);
            });
        }
    },[watch('payment_method')]);

    useEffect(() => {
        if(!((isSavedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)) || !isCreatePOModule)){
            if(getValues('price') && getValues('shipping_details.zip') && getValues('shipping_details.state_id')){
                setHandleSubmitValidation(false);
                calculateTotalPricing(true).then(()=>{
                    setHandleSubmitValidation(true);
                });
            }
            else{
                    setValue("sales_tax", parseFloat(0));
                    setValue("depositAmount", parseFloat(0));
            }
        }
    }, [debouncedMatTotal])

    useEffect(() => {
        if(!(isSavedBom && (createPoDataFromSavedBom || watch('is_draft_po')))){
            onStateZipChange()
        }
    }, [watch('shipping_details.zip'), watch('shipping_details.state_id')])

    const onStateZipChange = async()=>{
        const isValid:Boolean = await handleStateZipValidation();
        if(isValid && isCreatePOModule) handleSalesTax();
    }

    useEffect(() => {
        if(!(isSavedBom && createPoDataFromSavedBom && !watch('is_draft_po'))){
            calculateTotalPrice();
            if(!watch('is_draft_po')){
                setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));
            }
        }
    }, [watch('price'), watch('sales_tax'), watch('depositAmount')]);

    useEffect(()=>{
        if(debouncedTotalWeight && watch('cart_items') && location.pathname !== routes.savedBom){
            handlePriceIntegration();
        }
    },[debouncedTotalWeight])

 useEffect(() => {
    if (watch('price') > 0 && watch(`shipping_details.zip`).length === 5   && location.pathname !== routes.savedBom) {
      const formData = watch();
      if (formData?.cart_items?.length) {
        handlePriceIntegration();
      }
    }
  }, [discountData, productMapping, referenceDataUpdated, watch(`shipping_details.zip`), watch('shipping_details.state_id')]);

    useEffect(() => {
        const handleScrollOutsideDiv = (event) => {
            setIsCalendarOpen(false)
        };
        createPoContainerRef?.current?.addEventListener('scroll', handleScrollOutsideDiv);
        return () => {
            createPoContainerRef?.current?.removeEventListener('scroll', handleScrollOutsideDiv);
        };
    }, []);

    useEffect(()=>{
        if(scrollToBomLine){
            handleScrollToBomLine(scrollToBomLine)
        }
    },[scrollToBomLine])
    useEffect(()=>{
        if(!isCreatePOModule && fields?.length > 0){
            setStartIndex(0);
            setPrevStartIndex(0);
            setEndIndex(10);
            handleFilterFieldsData();
        }
    },[bomSummaryViewFilter , bomProductMappingDataFromSavedBom , fields])

    useEffect(()=>{
        if(buyerSetting){
            initialzeBnplSettings()
        }
    },[buyerSetting])

    const handleFilterFieldsData = () => {
        const filteredFieldsData = fields.filter((item: any, index: number) => {
            const lineStatus = watch(`cart_items.${index}`)?.lineStatus
            const atLeastOneValid = watch('cart_items').some((item: any, index: number) => {
                const lineStatus = watch(`cart_items.${index}`)?.lineStatus;
                return (
                    bomSummaryViewFilter === 'all' ||
                    (bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) ||
                    (bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending)
                );
              });
            const checkValidation = (( bomSummaryViewFilter === 'all' ||  !atLeastOneValid )  || ( bomSummaryViewFilter === 'red' && lineStatus === uploadBomConst.lineItemStatus.pending) || ( bomSummaryViewFilter === 'green' && lineStatus !== uploadBomConst.lineItemStatus.pending))
            return checkValidation;
        })
        setFilteredFields(filteredFieldsData);
    }

    const handleCreatePoDataFromSavedBom = ()=>{
        const _createPoDataFromSavedBom = {...createPoDataFromSavedBom};
        const cartItem = _createPoDataFromSavedBom?.items?.map((item, i) => ({
            bom_line_id: item.id || i,
            lineStatus: item.status,
            originalStatus: item.original_line_status || item.status,
            confidence: item.confidence,
            product_tag: item.product_tag,
            description: item.description,
            specification: item.specification,
            search_string: item.search_string,
            matched_products: item.matched_products,
            selected_products: item.selected_products,
            current_page: item.current_page,
            total_pages: item.total_pages,
            product_index: item.product_index,
            grade: item.grade,
            qty: item.qty,
            qty_unit: item.qty_unit,
            length: item.length,
            weight_per_quantity: item.weight_per_quantity,
            matched_product_count: item.matched_product_count,
            price_unit: item.price_unit,
            price: item.price_per_unit,
            line_weight: item.line_weight ?? '0.00',
            extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
            domesticMaterialOnly: item?.domestic_material_only || false,
            product_id: item.product_id,
            draft_line_id: item.id
        }));

        if (cartItem) {
            cartItem.forEach((item, index) => {
                if(item?.product_id){
                    const product = productMapping[item.product_id];
                    if(product){
                        item.descriptionObj = product;
                    }
                }else if(item?.selected_products?.length){
                    const selectedProducts = item.selected_products;
                    const hasSelectedProducts = selectedProducts.length > 0;
                    if (hasSelectedProducts) {
                        const product = productMapping[selectedProducts[0]];
                        // Directly set the values on the cartItem object
                        item.descriptionObj = product;
                    }
                }
            });
        }
        setValue('cart_items', cartItem);
        setInitialData({...createPoDataFromSavedBom, cart_items: cartItem});
    }

    const getInitialData = ()=>{
        if(location.pathname === routes.bomUploadReview && location.state?.from === 'bomPdfExtractor'){
            return uploadBomInitialData;
        }
        return initialData;
    }

    const initializeCreatePOData = async ()=>{
        try{  
            const _intialData = getInitialData();
            const deliveryDateList = await getDeliveryDateData();
            let buyingPreference = {...buyerSetting};
            initialzeBnplSettings()
            await getDeliveryAddresses()
            // const  res = await getBuyingPreference.mutateAsync()
            buyingPreference.userType = userRole.buyerUser;
            buyingPreference.stateRef = referenceData?.ref_states;
            setBuyingPreference(buyingPreference);
            const deliveryReceivingHours = buyingPreference.user_delivery_receiving_availability_details;
            if(deliveryReceivingHours?.length !== 0){
                setValue('recevingHours', deliveryReceivingHours)
            }
            const deliveryDaysAddValue = buyingPreference.delivery_days_add_value ?? referenceData?.ref_delivery_date[0]?.days_to_add;
            setValue('delivery_date_offset', deliveryDaysAddValue);
            let disableDeliveryDates = true;
            deliveryDateList.forEach(deliveryDateObj => {
                // if(deliveryDateObj.days_to_add === deliveryDaysAddValue){
                //     const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
                //     setValue('delivery_date', dayjs(deliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
                // }
                if(!deliveryDateObj.disabled && disableDeliveryDates){
                    disableDeliveryDates = false;
                }
            });
            setDisableDeliveryDate(disableDeliveryDates)
            if (_intialData) {
                setValue('id', _intialData?.id ?? '');
                if(_intialData?.cameFromSavedBom && !isHeaderDetailsConfirmed){
                    handleHeaderDetailsFill()
                }else{
                    if(!isHeaderDetailsConfirmed){
                        setValue('internal_po_number', _intialData.internal_po_number);
                        setValue('shipping_details', _intialData.shipping_details);
                        setValue('delivery_date', _intialData.delivery_date);
                        setValue('order_type', _intialData.order_type);
                        setStateDropDownValue(referenceData?.ref_states?.find(state => state.id === _intialData?.shipping_details?.state_id)?.code || '');
                    }
    
                    if(_intialData?.cart_items?.length > 0){
                    setValue('cart_items', _intialData.cart_items);
                    setValue('seller_price', _intialData?.seller_price ?? '0');
                    setValue('price', _intialData?.price ?? '0');


                        if(_intialData?.cart_items?.length > 0 && !(isSavedBom && createPoDataFromSavedBom)){
                            _intialData?.cart_items?.forEach((product: any, index: number) => {
                                if(product?.descriptionObj  && Object.keys(product.descriptionObj).length > 0){
                                    resetQtyAndPricePerUnitFields(index,product.descriptionObj)
                                    setDisableBidBuyNow(true)
                                };
                                    // updateValue(index)
                                });
                            if(location.pathname !== routes.savedBom) await handlePriceIntegration();
                        }
                    }
                    setValue('payment_method', _intialData?.payment_method ?? '');
                    setValue('sales_tax', _intialData?.sales_tax ?? 0);
                    setValue('delivery_date_offset',_intialData?.delivery_date_offset)
                    if(_intialData?.bom_id){
                        setValue('bom_id', _intialData?.bom_id ?? '');
                    }
                    setValue('is_draft_po', _intialData?.is_draft_po ?? false);
                    if(isSavedBom && createPoDataFromSavedBom){
                        setValue('price', _intialData?.material_total)
                        setValue('totalPurchase', _intialData?.total_purchase)
                        setValue('depositAmount', _intialData?.deposit)
                        setValue('subscriptionAmount', _intialData?.subscription)
                        setValue('totalWeight', parseInt(_intialData?.total_weight) ?? 0);    
                    }else{
                        setValue('totalWeight', calculateBuyerTotalOrderWeightForGear(watch('cart_items')));    
                        calculateMaterialTotalPrice();
                        }
                }
            }else{
                // setValue('internal_po_number', 'test');
                // setValue('order_type', 'BUY')
                // setValue('shipping_details.line1', buyingPreference.delivery_address_line1)
                // setValue('shipping_details.city', buyingPreference.delivery_address_city)
                // setValue('shipping_details.state_id', buyingPreference.delivery_address_state_id)
                // setStateDropDownValue(referenceData?.ref_states?.find(state => state.id === buyingPreference?.delivery_address_state_id)?.code || '');
                // setValue('shipping_details.zip', buyingPreference.delivery_address_zip)
                SetSendInvoicesToEmailData(buyingPreference.send_invoices_to);
                if (buyingPreference.default_payment_method === 'ACH_CREDIT') {
                    // setValue('payment_method', 'ach_credit')
                    setValue('depositAmount',0);
                } else if (buyingPreference.default_payment_method === 'BUY_NOW_PAY_LATER') {
                    // setValue('payment_method', 'bryzos_pay')
                }
                setValue('sales_tax', 0);
            }
            setDefaultPaymentOption(buyingPreference, paymentMethods, false);
            setTodayDate(new Date());
            setShowLoader(false);
            setIsAllCartDataLoaded(true);
         
        }catch(err) {
            setOpenErrorDialog(true);
            setErrorMessage("Something went wrong. Please try again in sometime");
            setShowLoader(false);
            console.error(err)

        }finally{
            setCreatePoData(null);
            if(isCreatePOModule){
                setUploadBomInitialData(null);
            }
        }
    }

    const handleHeaderDetailsFill = () => {
        setCameFromSavedBom(true);
        setValue('internal_po_number', initialData.internal_po_number);
        setValue('shipping_details', initialData.shipping_details);
        setValue('delivery_date', initialData.delivery_date);
        setValue('order_type', initialData.order_type);
        setStateDropDownValue(referenceData?.ref_states?.find(state => state.id === initialData?.shipping_details?.state_id)?.code || '');
    }

    const getLastModifiedBom = (key: string): string | null => {
        try {
          const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
          if (lastModifiedBom) {
            const lastModifiedBomData = JSON.parse(lastModifiedBom);
            if (key in lastModifiedBomData) {
              return lastModifiedBomData[key];
            } else {
              return null;
            }
          } else {
            return null;
          }
        } catch (e) {
          console.warn('Error checking key in localStorage', e);
          return null;
        }
      };
    
    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus: string = "description") => {
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="qty-input-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else{
            const childElement = parentElement.querySelector('[id^="combo-box-demo"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const handleScrollToBomLine = (bomLineId: string) => {

        const isBomLineIdPresent = filteredFields.slice(startIndex, endIndex).some((item: any) => item.bom_line_id === bomLineId);
        if(!isBomLineIdPresent){
            const index = bomUploadResult.findIndex((item: any) => item.bom_line_id === bomLineId);
            console.log('index bomuplode ', index);
            if(index !== -1){
                const _startIndex = index - (index + 1) % 5;
                setStartIndex(_startIndex>=0?_startIndex:0);
                setPrevStartIndex(_startIndex>=0?_startIndex:0);
                setEndIndex((_startIndex + 10)>=0?(_startIndex + 20):0);
                setTimeout(()=>{
                    scrollBomLineToSpecificIndex(bomLineId);
                },500)
            }
        }else{
            scrollBomLineToSpecificIndex(bomLineId);
        }

    }

    const scrollBomLineToSpecificIndex = (bomLineId: string)=>{
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            table.scrollTo({ top: elementOffsetTop - 200 });
        };
        const table = addPoLineTableRef.current;
        const container = createPoContainerRef.current;
        const element = document.getElementById(bomLineId);

        if (container) {
            container.scrollTo({ top: 244, behavior: 'smooth' });
        }

        if (table && element) {
            scrollToElement(table, element);
            focusChildElement(element);
        }
        setScrollToBomLine(null)
    }

    const initialzeBnplSettings = ()=>{
        const {buyerSetting} = useBuyerSettingStore.getState();
        if(buyerSetting?.bnpl_settings){ 
            setValue('availableCreditLimit', buyerSetting?.bnpl_settings?.bryzos_available_credit_limit)
            setValue('bnplStatus', buyerSetting?.bnpl_settings?.bnpl_status);
            if(buyerSetting?.bnpl_settings?.requested_credit_limit){
                setValue('requestedCreditLimit', buyerSetting?.bnpl_settings?.requested_credit_limit)
            }else{
                setValue('requestedCreditLimit', 0)
            }
            setValue('max_restricted_amount' , buyerSetting?.bnpl_settings?.max_restricted_amount ??"0")
        }else{
            setValue('availableCreditLimit', 0)
            setValue('bnplStatus', BNPLSTATUS.REJECTED);
            setValue('requestedCreditLimit', 0)
            setValue('max_restricted_amount' , "0")
        }
    }
    const calculateTotalPrice = ()=>{
        if(!(isSavedBom && createPoDataFromSavedBom && !watch('is_draft_po'))){
            const materialTotal = +(watch('price') ?? 0);
            let totalPurchaseOrderPrice: number = 0;
            if (materialTotal) {
            totalPurchaseOrderPrice += materialTotal + (+(watch('sales_tax') ? watch('sales_tax') : 0)) + (+(watch('depositAmount') ?? 0))
            }
            setValue('totalPurchase',parseFloat(totalPurchaseOrderPrice));
        }
    }
    useEffect(() => {
        if(deboucedSearchString && isCreatePOModule){
            handleCreatePOSearch(searchStringData, null, lineSessionId)
        }
    }, [deboucedSearchString])

    const updateLineProduct = (index, product) => {

        setSelectedProduct(product);

        setValue(`cart_items.${index}.qty`, '')
        setValue(`cart_items.${index}.extended`, 0)
        setValue(`cart_items.${index}.seller_extended`, 0)
        setValue(`cart_items.${index}.price`, 0)
        setValue(`cart_items.${index}.seller_price`, 0);
        setValue(`cart_items.${index}.price_unit`, '')
        setValue(`cart_items.${index}.qty_unit`, '')
        setValue(`cart_items.${index}.domesticMaterialOnly`, null)
        clearErrors(`cart_items.${index}.qty`)
        if(product){
            // resetQtyAndPricePerUnitFields(index, product);
            if (userPartData && Object.keys(userPartData)?.length) {
                setValue(`cart_items.${index}.product_tag`, userPartData[product?.Product_ID])
            }
        }
        else{
            calculateMaterialTotalPrice();
        }
    }

    const updateLineProductTag = (index, product) => {
        if(userPartData && Object.keys(userPartData)?.length){
            setValue(`cart_items.${index}.product_tag`, userPartData[product?.Product_ID])
        }else{
            setValue(`cart_items.${index}.product_tag`, "")
        }
    }

    const resetQtyAndPricePerUnitFields = (index, product) => {
        const qtyUnitMData = product.QUM_Dropdown_Options.split(",")
        setValue(`cart_items.${index}.qty_um`,qtyUnitMData);
        if (!initialData?.cart_items?.[index]?.qty_unit) {
            setValue(`cart_items.${index}.qty_unit`, qtyUnitMData[0])
        }
        resetPricePerUnitFields(index, product);
    }

    const resetPricePerUnitFields = (index, product) => {
        const priceUnitMData = product?.PUM_Dropdown_Options?.split(",").filter((item:any) => !(item.trim().toLowerCase() === priceUnits.net_ton || item.trim().toLowerCase() === 'net ton'));

        setValue(`cart_items.${index}.price_um`,priceUnitMData);
        if (!initialData?.cart_items?.[index]?.price_unit) {
            let priceUnit = priceUnitMData[0];
            if(initialData?.cart_items?.[index]?.bom_line_id && initialData?.cart_items?.[index]?.qty_unit) priceUnit = initialData?.cart_items?.[index]?.qty_unit;
            setValue(`cart_items.${index}.price_unit`, priceUnit)
        }else{
            setValue(`cart_items.${index}.price_unit`, initialData?.cart_items?.[index]?.price_unit)
        }
        pricePerUnitChangeHandler(index, product);
    }

    const removeLineItem = (index)=>{
        remove(index);
        calculateMaterialTotalPrice();
    }

    const updateValue = async (index: number, priceMapping: any = {})=>{
        if(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0){

        const _selected = getValues(`cart_items.${index}`).descriptionObj;
        const productId = watch(`cart_items.${index}.descriptionObj.Product_ID`)
        const isSafeProductCode = watch(`cart_items.${index}.descriptionObj.is_safe_product_code`)
        const searchZipCode = watch(`shipping_details.zip`) 
        const priceUnit = getValues(`cart_items.${index}.price_unit`) ;

        if(!priceUnit) resetPricePerUnitFields(index, _selected)

        let selectedPriceUnit = watch(`cart_items.${index}.price_unit`) 
        const prices = priceMapping[productId];

        const pumDropdownOptions = watch(`cart_items.${index}.price_um`);
        pumDropdownOptions.forEach((item: any)=>{
            if(item.toLowerCase() !== "net ton" || item.toLowerCase() !== priceUnits.net_ton){
                setValue(`cart_items.${index}.descriptionObj.${newPricingPrefix}${item}`, prices?.[item] || prices?.[item.toLowerCase()])
            }
        })
        // setPriceData(result.price);
        if(_selected && Object.keys(_selected).length > 0){
            const updateQtyUnit = watch(`cart_items.${index}.qty_unit`).toLowerCase();
            const updatePriceUnit = watch(`cart_items.${index}.price_unit`).toLowerCase();
            setValue(`cart_items.${index}.qty_unit`, updateQtyUnit);
            setValue(`cart_items.${index}.price_unit`, updatePriceUnit);
            updateLineItem(index);
            const qtyVal = watch(`cart_items.${index}.qty`);
            const qtyUnit = watch(`cart_items.${index}.qty_unit`);
            const priceUnit = watch(`cart_items.${index}.price_unit`) ;
            if(!qtyUnit) {
                setValue(`cart_items.${index}.qty_um`, _selected.QUM_Dropdown_Options.split(","));
                setValue(`cart_items.${index}.qty_unit`, _selected.QUM_Dropdown_Options.split(",")[0].toLowerCase());
            }
            const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
            const updatedUnit = unit;
            const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
            if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                if(!priceUnit) resetPricePerUnitFields(index, _selected)
            }else{
                if(qtyVal < 1) clearErrors(`cart_items.${index}.qty`);
                setValue(`cart_items.${index}.price_unit`, '');
                setValue(`cart_items.${index}.price`, 0);
                setValue(`cart_items.${index}.seller_price`, 0);
            }
            calculateMaterialTotalPrice();
        }
        }
    }

    const validateSavedBomCreatePo = (index) => {
        const item = watch(`cart_items.${index}`);
        let descriptionObj = watch(`cart_items.${index}`)?.descriptionObj;
        if (item?.selected_products?.length && !descriptionObj) {
            const selectedProducts = item.selected_products;
            const hasSelectedProducts = selectedProducts.length > 0;
            if (hasSelectedProducts) {
                const product = productMapping[selectedProducts[0]];
                // Directly set the values on the cartItem object
                descriptionObj = product;
            }
        }
        if (descriptionObj && Object.keys(descriptionObj).length > 0) {
            const _selected = descriptionObj;

            if (_selected) {
                const qtyVal = +getValues(`cart_items.${index}.qty`) || 0;
                const qtyUnit = getValues(`cart_items.${index}.qty_unit`);
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit , orderIncrementPrefix);
                if (qtyVal && orderIncrement) {
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        clearErrors(`cart_items.${index}.qty`);
                        trigger(`cart_items.${index}.qty`);
                        return true;
                    } else {
                        if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` }, { shouldFocus: false })
                        if (qtyVal === null) setValue(`cart_items.${index}.qty`, '');
                        setValue(`cart_items.${index}.extended`, 0);
                        setValue(`cart_items.${index}.seller_extended`, 0);
                    }
                }
            }
        }
    }

    const initializeBomTileData = (index: number) => { 
        const selectedProduct = watch(`cart_items.${index}.selected_products`);
        if (selectedProduct?.length > 0) {
          const product = productMapping[selectedProduct[0]];
          setValue(`cart_items.${index}.descriptionObj`, product);
          setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options.split(","));
          
          // Only set qty_unit if 'Ea' is not in QUM options
          const qumOptions = product.QUM_Dropdown_Options.split(",");
          if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
            setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
          }
        }
    }

    const updateLineItem = (index: any) => {      
        const _selected = getValues(`cart_items.${index}.descriptionObj`);

        if(watch(`cart_items.${index}.lineStatus`) === "SKIPPED"){
            return;
        }

        if (_selected) {
            const qtyVal = +getValues(`cart_items.${index}.qty`) || 0;
            const qtyUnit = getValues(`cart_items.${index}.qty_unit`);
            const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options.split(",")[0] : '')).toLowerCase();
            const updatedUnit = unit;
            const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
            pricePerUnitChangeHandler(index, undefined);
            if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                const buyerPricePerUnit = parseFloat(getValUsingUnitKey(_selected, unit, newPricingPrefix));
                setValue(`cart_items.${index}.buyer_calculation_price`, buyerPricePerUnit);
                setValue(`cart_items.${index}.seller_calculation_price`, buyerPricePerUnit);
                const totalVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                const extendedValue = buyerPricePerUnit * qtyVal;
                const totalSellerVal = parseFloat((buyerPricePerUnit * qtyVal).toString()).toFixed(2);
                setValue(`cart_items.${index}.extended`, +totalVal);
                setValue(`cart_items.${index}.seller_extended`, +totalSellerVal);
                setValue(`cart_items.${index}.extendedValue`, +extendedValue);
                clearErrors(`cart_items.${index}.qty`);
                trigger(`cart_items.${index}.qty`);
            } else {
                if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` })
                if (qtyVal === null) setValue(`cart_items.${index}.qty`, '');
                setValue(`cart_items.${index}.extended`, 0);
                setValue(`cart_items.${index}.seller_extended`, 0);
            }
        }
      }

    const pricePerUnitChangeHandler = (index: any, product: any , priceData: any = 0) => {
        product = product ?? getValues(`cart_items.${index}.descriptionObj`);
        if (product) {
            const unit = getValues(`cart_items.${index}.price_unit`) || product.PUM_Dropdown_Options.split(",")[0];
            const umVal = getValUsingUnitKey(product, unit, newPricingPrefix);
            setValue(`cart_items.${index}.price`, umVal);
            setValue(`cart_items.${index}.seller_price`, umVal);
        }
        else {
            setValue(`cart_items.${index}.price`, 0);
            setValue(`cart_items.${index}.seller_price`, 0);
        }
      }

    const onSubmit = (data) => {
        const date = new Date()
        if(dayjs(date).format('M/D/YYYY') === dayjs(todayDate).format('M/D/YYYY')){
            setShowLoader(true);
            const totalPurchaseValue = getValues('price');
            const totalSellerPurchaseValue = parseFloat(getValues('seller_price')).toFixed(2);
            const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
            data.shipping_details.line2 = data.shipping_details.line2?.trim() || null;
            const payload = {
                "data": {
                    "internal_po_number": data.internal_po_number,
                    "shipping_details": data.shipping_details,
                    "delivery_date": dayjs(data.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                    "payment_method": getValues('payment_method'),
                    "seller_price": totalSellerPurchaseValue, 
                    "price": String(totalPurchaseValue),
                    "sales_tax": data.sales_tax,
                    "freight_term": "Delivered",
                    "cart_items": formatCartItems(getValues('cart_items')),
                    "checkout_local_timestamp": localDateTime,
                    "delivery_date_offset": data.delivery_date_offset,
                    "order_type": data?.order_type ?? '',
                    "order_size": String(data?.totalWeight) ?? ''
                }
            };

            if(watch('bom_id') && !watch('is_draft_po')){
                payload.data.bom_id = watch('bom_id')
            } else if (watch('is_draft_po') && watch('bom_id')){
                payload.data.draft_id = watch('bom_id')
            }
            setDisableCloseAnalytic(false)
            saveBuyerCheckout.mutateAsync(payload)
                .then(res => {
                    if (res.data.data.error_message) {
                        setOpenErrorDialog(true);
                        setErrorMessage(res.data.data.error_message);
                        setShowLoader(false);
                        saveUserActivity(null, res.data.data.error_message)
                        setDisableCloseAnalytic(true)
                    } else {
                        queryClient.invalidateQueries([reactQueryKeys.getUserPartData]);
                        if(watch('bom_id') && !watch('is_draft_po')){
                            setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        } else if (watch('is_draft_po') && watch('bom_id')){
                            setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        }
                        const payload = {
                            "data": {
                                "session_id": sessionId,
                                "close_status": "ACCEPT"
                            }
                        }
                        logUserActivity.mutateAsync({url:import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close' ,payload})
                        .catch(err => console.error(err))
                        navigate(routes.orderConfirmationPage, { state: { poNumber: res.data.data, jobNumber: data.internal_po_number, sendInvoicesTo: sendInvoicesToEmailData, selectedOptionPayment: data.payment_method, } })
                        setCreatePoData(null);
                        saveUserActivity(res.data.data, null);
                    }
                })
                .catch(err => {
                    saveUserActivity(null, (err?.message ?? err));
                    setDisableCloseAnalytic(true)
                    setOpenErrorDialog(true);
                        setErrorMessage("Something went wrong. Please try again in sometime");
                        setShowLoader(false);
                    console.error(err)
                })
        }else{
            setOpenErrorDialog(true);
            setErrorMessage('Selected delivery date is incorrect, please select correct delivery date.');
            setValue('delivery_date', null)
            getDeliveryDateData();
            setTodayDate(new Date());
        }

        
    }

    const addNewRow = () => {
        append({...cartItem});
        // const t = setTimeout(() => {
        //     clearTimeout(t);
        //     const element = document.getElementById(`combo-box-demo${fields.length}`);
        //     if (element) {
        //         element.focus()
        //     }
        // }, (100));
    }
    const formatCartItems = (products, draftId = null) => {
        return products
        .filter((item: any) => draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.lineStatus !== "SKIPPED"))
        .map((item, index) => {
            if (draftId ? (item?.draft_line_id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0)) : ((item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.lineStatus !== "SKIPPED")) {
                const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
                let formattedQtyUnit = '';
                let formattedPriceUnit = '';
                if(draftId && item?.draft_line_id && !item?.qty_unit){
                    formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
                }else{
                    formattedQtyUnit = item?.qty_unit?.toUpperCase();
                }

                if(draftId && item?.draft_line_id && !item?.price_unit){
                    formattedPriceUnit = initialData?.cart_items[index]?.price_unit
                }else{
                   formattedPriceUnit = item?.price_unit?.toUpperCase();
                }
                const cartItem = {
                    "line_id": index + 1,
                    "description": item?.descriptionObj?.UI_Description ?? '',
                    "qty": (draftId && item?.draft_line_id && !item?.qty) ? initialData?.cart_items[index]?.qty : item?.qty || '',
                    "qty_unit": formattedQtyUnit,
                    "product_tag": item?.product_tag ?? '',
                    "product_tag_mapping_id": item?.descriptionObj?.product_tag ?? '',
                    "price": (draftId && item?.draft_line_id && !item?.price) ? initialData?.cart_items[index]?.price_per_unit : item?.price ?? '',
                    "price_unit": formattedPriceUnit,
                    "extended": item?.extended ?? '',
                    "product_id": (draftId && item?.draft_line_id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? '',
                    "reference_product_id": item?.descriptionObj?.id ?? '',
                    "shape": item?.descriptionObj?.Key2 ?? '',
                    "seller_price": item?.seller_price ?? '',
                    "seller_extended": item?.seller_extended?.toFixed(2) ?? '',    
                    "buyer_pricing_lb": item?.descriptionObj?.[buyerPricingLbKey] ?? '',
                    "domestic_material_only": item?.domesticMaterialOnly ?? false,
                    "buyer_calculation_price": item?.buyer_calculation_price ?? '',
                    "seller_calculation_price": item?.seller_calculation_price ?? '',
                    "lineStatus": item?.lineStatus ?? '',
                    "id": draftId ? item?.draft_line_id : undefined,
                    "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined
                };
                return cartItem
            }
            return null
        });
    }

    const handleGetDeliveryDate = (delivery_date,recevingHours) => {
        const date = dayjs(delivery_date)
        const convertDate = date.format('dddd')
        let checkDeliveryDate = null;
        recevingHours?.forEach((recevingHour)=>{
            if(recevingHour.is_user_available === 1 && convertDate.toLowerCase() === recevingHour.day.toLowerCase()){
                checkDeliveryDate = delivery_date
            }
        })
        return checkDeliveryDate;
    }


    const saveUserActivity = (checkOutPoNumber, checkOutError) => {
        if(isCreatePOModule){
            saveCreatePOUserActivity(sessionId, checkOutPoNumber, checkOutError);
        }
    }

    const saveBomHeaderDetails = () => {
        if (location.pathname === routes.bomUploadReview || location.pathname === routes.savedBom) {
            if(currentBomData?.id){
                setBomDataIdToRefresh(currentBomData?.id);
            }
            try {
                const payload = {
                    "data": {
                        "bom_upload_id": currentBomData?.id ?? '',
                        "bom_name": watch('internal_po_number') ?? '',
                        "bom_type": watch('order_type') ?? '',
                        "delivery_date": watch('delivery_date') ?? '',
                        "shipping_details": {
                            "line1": watch('shipping_details.line1') ?? '',
                            "line2": watch('shipping_details.line2')?.trim() || null,
                            "city": watch('shipping_details.city') ?? '',
                            "zip": watch('shipping_details.zip') ?? '',
                            "state_id": watch('shipping_details.state_id') ?? ''
                        }
                    }
                }
                if(currentBomData?.id && payload.data.bom_name && payload.data.bom_type && payload.data.delivery_date && payload.data.shipping_details.line1 && payload.data.shipping_details.city && payload.data.shipping_details.zip && payload.data.shipping_details.state_id){
                    saveModifiedBomHeader();
                    mutateSaveBomHeaderDetails.mutateAsync(payload)
                    let _updatedBomInitialData = { 
                        ...uploadBomInitialData,
                        internal_po_number: watch('internal_po_number'),
                        shipping_details: watch('shipping_details'),
                        delivery_date: watch('delivery_date'),
                        order_type: watch('order_type')
                    }
                    setUploadBomInitialData(_updatedBomInitialData)
                }
            } catch (error) {
                console.error(error)    
            }
        }
    }
    const saveModifiedBomHeader = () => {
        try {
          if(currentBomData?.id ){
            const data ={
              [currentBomData?.id]: "HEADER"
            }
            const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if(_lastModifiedBom){
              const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
              _lastModifiedBomData[currentBomData?.id] = "HEADER";
              localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
            }else{
              localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
            }
          }
        } catch (e) {
          console.warn('Could not store value in localStorage', e);
        }
      }


    const handleCreatePOSearch = (searchStringKeyword, status, descriptionLineSessionId) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "keyword" : searchStringKeyword,
                "status" : status,
                "source" : 'create-po',
                "line_session_id": descriptionLineSessionId,
            }
        }
        setSelectedProduct(null)
        saveProductSearch.mutateAsync(payload)
        .catch(err => console.error(err))
    }

    const calculateLineWeight = (data) => {
        let lineWeight = 0;
        const qty = data.qty ? parseFloat(data.qty.replace(/[\$,]/g, '')) : 0;
        if(data?.descriptionObj && Object.keys(data?.descriptionObj).length > 0){
            const qtyUnit = data.qty_unit ? data.qty_unit : data.descriptionObj.QUM_Dropdown_Options.split(",")[0];
            const orderIncrementLb = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.lb}`]?.replace(/[\$,]/g, '')) || 0
            const orderIncrementFt = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${priceUnits.ft}`]?.replace(/[\$,]/g, '')) || 0;
            const lbsPerFt = orderIncrementLb / orderIncrementFt
            const orderIncrementFtPrice = lbsPerFt * orderIncrementFt
            const actualOrderIncrement = parseFloat(data.descriptionObj[`${orderIncrementPrefix}${qtyUnit.toLowerCase()}`]?.replace(/[\$,]/g, '')) || 0;
            lineWeight = orderIncrementFtPrice * qty / actualOrderIncrement;
        }
        return formatToTwoDecimalPlaces(lineWeight);
    }
    
    const getExportPoData = ()=>{
        const paymentMethod = getValues('payment_method') || 'bryzos_pay'; 
        return { ...getValues(), selectedOptionPayment: paymentMethod };
    }

    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = getValues('cart_items');
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.lineStatus !== "SKIPPED" || item?.lineStatus === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 && 
                   Boolean(item.qty_unit) &&
                   Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    useEffect(() => {
        const subscription = watch((_, { name }) => {
            if (name?.startsWith('cart_items')) {
                setIsCartValid(validateCart());
            }
        });
        return () => subscription.unsubscribe();
    }, [watch]);

    const getCartItems = () =>{
        const {cart_items} = getValues();
        const formattedItems = cart_items.filter(item => item?.descriptionObj?.UI_Description || item?.lineStatus).map((item, index) => ({
            description: descriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            otherDescription: getOtherDescriptionLines(item?.descriptionObj?.UI_Description ?? ''), 
            product_tag: item?.product_tag ?? '',
            domesticMaterialOnly: item?.domesticMaterialOnly ? '\nDomestic (USA) Material Only' : '',
            qty: formatToTwoDecimalPlaces(item?.qty ?? ''),
            qty_unit: item?.qty_unit?.toLowerCase() ?? '',
            price_unit: item?.price_unit?.toLowerCase() ?? '',
            extended: formatToTwoDecimalPlaces(item?.extended ?? ''),
            price: item?.price_unit?.toLowerCase() === priceUnits.lb ? format4DigitAmount(item?.price ?? '') : formatToTwoDecimalPlaces(item?.price ?? ''),
            line_weight: location.pathname === routes.savedBom ? formatToTwoDecimalPlaces(item?.line_weight ?? '') : calculateLineWeight(item),
            line_weight_unit: "Lb", 
            line_no: index,
            po_line: index.toString(),
            descriptionObj:item?.descriptionObj ?? {},
            extendedValue:item?.extendedValue ?? '',
            lineStatus: item?.lineStatus ?? ''
        }));
        return formattedItems
    }

    const getDeliveryDateData = async () => {
        let deliveryDateList = []; 
        const res = await getDeliveryDate.mutateAsync();
        if(res?.data?.data){
            deliveryDateList = res.data.data
        }
        const optionMap = deliveryDateList.reduce((acc, option) => {
            acc[option.value] = option;
            return acc;
        }, {});
        setDeliveryDateMap(optionMap)
        setDeliveryDates(deliveryDateList);
        return deliveryDateList
    }

    const handleDeliveryToClick = () => {
        setOpenDeliveryToDialog(true);
        setIsFocused(true);
    }

    const handleDeliveryInfoContainerClickAway = () => {
        if (errors?.shipping_details?.zip?.message || errors?.shipping_details?.state_id?.message || errors?.shipping_details?.city?.message || errors?.shipping_details?.line1?.message || errors?.shipping_details?.line2?.message) {
            setOpenDeliveryToDialog(true)
            setIsFocused(true)
        } else {
            setStateInputFocus(false)
            setOpenDeliveryToDialog(false)
            setIsFocused(false)
        }
    }

    const CustomCalendarHeader = (props) => {
        const { 
          currentMonth, 
          onMonthChange,
          reduceAnimations,
          views,
          view,
          onViewChange,
          ...other 
        } = props;
        
        const monthName = dayjs(currentMonth).format('MMMM');
        const isCurrentMonth = dayjs(currentMonth).isSame(dayjs(), 'month');
        const isNextMonth = dayjs(currentMonth).isSame(dayjs().add(1, 'month'), 'month');
        const handlePreviousMonthClick = () => {
          onMonthChange(dayjs(currentMonth).subtract(1, 'month'), 'left');
        };
        
        const handleNextMonthClick = () => {
          onMonthChange(dayjs(currentMonth).add(1, 'month'), 'right');
        };
        
        return (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            padding={2}
            className={styles.customCalendarHeaderBox}
          >
            <IconButton onClick={handlePreviousMonthClick} className={styles.customCalendarHeaderIcon}  disabled={isCurrentMonth}>
              <ArrowLeftIcon sx={{color: isCurrentMonth ? '#a0a0a0' : '#dfe2f0'}}/>
            </IconButton>
            
            <Typography variant="h6" className={styles.customCalendarHeaderText}>
              {monthName.toLocaleUpperCase()}
            </Typography>
            
            <IconButton onClick={handleNextMonthClick} className={styles.customCalendarHeaderIcon} disabled={isNextMonth}>
              <ArrowRightIcon  sx={{color: isNextMonth ? '#a0a0a0' : '#dfe2f0'}}/>
            </IconButton>
          </Box>
        );
      };

    const scrollToTop = () => {
        setScrollPosition(0);
        const container = createPoContainerRef.current;
        if (container) {
            container.scrollTo({ top: 0, behavior: 'smooth' });
        }
    };  

    const handleOpenCalendarBtn = () => {
        if (!disableDeliveryDate) {
            setIsCalendarOpen(true);
        } else {
            setOpenErrorDialog(true)
            setErrorMessage(mobileDiaglogConst.receivingHrsEmpty)
            setIsCalendarOpen(false)
            const nextElement = document.querySelector('[tabindex="15"]');
            if (nextElement instanceof HTMLElement) {
                nextElement.focus();
            }
        }
    }

    const allowedDates = useMemo(() => {
        return deliveryDates
            .filter(date => !date?.disabled)
            .map(date => new Date(date?.value));
    }, [deliveryDates]);

    const handleDateSelect = (date:any) => {
       const selectedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
       setValue('delivery_date_offset', deliveryDateMap?.[selectedDate]?.days_to_add);
       setIsCreatePoDirty(true)
    }

    const handleMainScroll = () => {
        // Skip if the scroll is from dragging the thumb
        // if (isDraggingScrollState) return;
        
        if (!createPoContainerRef.current) return;

        const { scrollTop, scrollHeight, clientHeight } = createPoContainerRef.current;
        const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5; // 5px threshold
        if(!isDraggingScrollState)setScrollPosition(scrollTop);
        const header = headerContainerRef.current;
        
        // Handle header opacity
        if (header) {
            let opacity = 0;
            if(scrollTop > 52){
                opacity = Math.min(scrollTop / 152, 1);
            }
            (header as HTMLElement).style.opacity = opacity.toString();
        }
        // Enable/disable the table scroll based on main scroll position
        if (addPoLineTableRef.current) {
                setHidePoLineScroll(!isAtBottom);
        }
    }

    const getScrollThumbHeight = (element: any) => {
        if (!element) return 0;
        
        // Get the visible height of the container
        const containerHeight = element.clientHeight;
        
        // Get the total scrollable height
        const scrollHeight = element.scrollHeight;
        
        // Calculate the ratio of visible area to total content
        const ratio = containerHeight / scrollHeight;
        
        // The thumb height is proportional to this ratio
        // Minimum height is often set to prevent tiny thumbs
        const thumbHeight = Math.max(30, Math.floor(containerHeight * ratio));
        
        setScrollThumbHeight(thumbHeight);
      }

    const handleLineItemScroll = () => {
        // Skip if the scroll is from dragging the thumb
        if(!addPoLineTableRef.current) return;
        const { scrollTop, scrollHeight, clientHeight } = addPoLineTableRef.current;
        if(!isDraggingScrollState)setScrollPosition(244 + scrollTop);
        const firstRow = addPoLineTableRef.current.querySelector('tbody tr:first-child');
        const firstRowHeight = firstRow?.clientHeight;
        const scrollThreshold = isCreatePOModule ? 150 : 50; // pixels from 
        const isAtBottom = 
            scrollHeight - (scrollTop + clientHeight) 
            <= scrollThreshold;
        if(isCreatePOModule){
            if (isAtBottom) {
                // Add 5 new rows
                for (let i = 0; i < 5; i++) {
                    append({...cartItem});
                }
                setEndIndex(endIndex + 5);
            }
        }
        else{
            handleViewIndexChange();
            const length = endIndex;
                if (isAtBottom) {
                    // Add 5 new rows
                    if(length < filteredFields?.length){
                        if(endIndex >= 20){
                            setStartIndex(startIndex + 5);
                            setPrevStartIndex(startIndex);
                        }
                        setEndIndex(endIndex + 5);
                    }
                }
                else if(startIndex > 0 && scrollTop <= firstRowHeight){
                    setStartIndex(startIndex - 5>=0?startIndex - 5:0);
                    setEndIndex(endIndex - 5);
                    setPrevStartIndex(startIndex);
                }
        }
    }

    const handleViewIndexChange = () => {
        const container = addPoLineTableRef.current;
        if (container) {
          const firstRow = container.querySelector('tbody tr:first-child');
          if (firstRow) {
            const { scrollTop } = container;
            const rect = firstRow.getBoundingClientRect();
      
            // Get computed styles to access margins
            const computedStyle = window.getComputedStyle(firstRow);
      
            // Calculate total height including margins
            const marginTop = parseInt(computedStyle.marginTop, 10);
            const marginBottom = parseInt(computedStyle.marginBottom, 10);
      
            // Total height = height + margin-top + margin-bottom
            const firstRowHeight = rect.height + marginTop + marginBottom;
            const index = Math.floor(scrollTop / firstRowHeight);
            setViewIndex(startIndex + index);
          } else {
            console.warn('First row not found');
          }
        }
      };

    //Side effect to readjust the scroll position when the start index changes for bom upload
    useEffect(()=>{
        if(addPoLineTableRef.current && !isCreatePOModule ){
            if(startIndex === 0){
                createPoContainerRef.current.style.overflowY = 'auto';
            }
            else{
                createPoContainerRef.current.style.overflowY = 'hidden';
            }
            const firstRow = addPoLineTableRef.current.querySelector('tbody tr:first-child');
            if(prevStartIndex < startIndex){
                
                // addPoLineTableRef.current.scrollTop = firstRow?.clientHeight * 13;
                addPoLineTableRef.current.scrollTop = addPoLineTableRef.current.scrollTop - (firstRow?.clientHeight * 5);
            }
            else if(prevStartIndex > startIndex){
                // addPoLineTableRef.current.scrollTop = firstRow?.clientHeight * 6 + 50;
                addPoLineTableRef.current.scrollTop = addPoLineTableRef.current.scrollTop + (firstRow?.clientHeight * 5);
            }
        }
    },[startIndex])

    const openAddLineTab = () => {
        if(!addPoLineTableRef.current) return;
        const container = addPoLineTableRef.current;
        if(container.scrollTop > 0 && orderInfoIsFilled && createPoContainerRef.current){
            createPoContainerRef.current.scrollTo({
                top: createPoContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            container.style.overflowY = 'auto';
            const formInputHeight = formInputGroupRef.current?.clientHeight;
            setTimeout(()=>{
                setScrollPosition(container.scrollTop + formInputHeight);
            },400)
        }
    }

    const handleScrollChange = (newScrollPosition: number) => {
        if(newScrollPosition <= 244){
            if(!createPoContainerRef.current || viewIndex !== 0)return;
            createPoContainerRef.current.scrollTop = newScrollPosition;
        }
        else{
            if(!addPoLineTableRef.current || !orderInfoIsFilled)return;
            addPoLineTableRef.current.scrollTop = newScrollPosition - 244;
        }
        setScrollPosition(newScrollPosition);

    }

    

    // Use useCallback to prevent recreation of this function on every render
    const setIsDraggingScroll = useCallback((isDragging: boolean) => {
      setIsDraggingScrollState(isDragging);
    }, []);

    const handleBomUploadScreenNavigation = () => {
        if (orderInfoIsFilled) {
            const { delivery_date, shipping_details , order_type, internal_po_number , delivery_date_offset} = getValues();
            const formattedCreatePoData = {
                delivery_date,
                shipping_details,
                order_type,
                internal_po_number, 
                delivery_date_offset
            }
            // setCreatePoData(formattedCreatePoData)
            setUploadBomInitialData(formattedCreatePoData)
            const isDirty = watch('cart_items')?.some(item => item.descriptionObj && Object.keys(item.descriptionObj).length > 0);
            const message = isCreatePOModule
              ? navigationConfirmMessages.unsavedChanges
              :
              navigationConfirmMessages.confirmLeave;
            //navigateWithConfirmation(isDirty, routes.bomUpload, undefined, message);
            navigateWithConfirmation(isDirty, routes.bomExtractor, undefined, message);
            // handleNavigateAway(routes.bomUpload);
        }
    }
    const handleJobPoInputRef = (e:any) => { 
        jobPoInputRef.current = e;
    }
    
    const handleSavePo = () => {
        setShowLoader(true);
        const data = watch();
        resetDialogStore();
        setIsCreatePoDirty(false);
        const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
        const shippingDetails = data.shipping_details;
        shippingDetails.line2 = data.shipping_details.line2?.trim() ? data.shipping_details.line2 : null;
        const payload = {
            "draft_id": watch('is_draft_po') ? data.id : '',
            "internal_po_number": data.internal_po_number,
            "shipping_details": data.shipping_details,
            "delivery_date": dayjs(data.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
            "payment_method": data.payment_method || undefined,
            "price": String(data.price),
            "sales_tax": data.sales_tax,
            "freight_term": "Delivered",
            "cart_items": formatCartItems(data.cart_items, data.id),
            "checkout_local_timestamp": localDateTime,
            "order_type": data?.order_type ?? '',
            "order_size": String(data?.totalWeight) ?? ''
        };
        postDraftPo.mutate(payload, {
            onSuccess: () => {
                setShowLoader(false);
                navigateWithConfirmation(false, routes.savedBom);
                setDisplayLeftPanel(false);
                setOpenLeftPanel(true);
            },
            onError: (error) => {
                showCommonDialog(null,error?.message || commomKeys.errorContent, commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
                setShowLoader(false);
            }
        });


    }

    const formatAddressDisplay = (address: any) => {
        if (!address) return '';
        const stateName = referenceData?.ref_states?.find(state => state.id === address?.state_id)?.code || '';
        
        const parts = [address.line1];
        if (address.line2 && address.line2.trim()) {
          parts.push(address.line2);
        }
        
        parts.push(address.city);
        parts.push(stateName);
        parts.push(address.zip);

        return parts.filter(Boolean).join(', ');
      };

    const handleAutocompleteTabSelection = (
        e: any, 
        field: any, 
        isOpen: boolean, 
        filterFields: string[], 
        setOpenState: (open: boolean) => void, 
        nextFieldSelector: string
    ) => {
        if (e.key === 'Tab' && !e.shiftKey) {
            // Handle Tab key to select highlighted option
            if (isOpen && deliveryAddressData?.length > 0) {
                e.preventDefault();
                const activeElement = document.activeElement;
                const activeDescendantId = activeElement?.getAttribute('aria-activedescendant');
                
                if (activeDescendantId) {
                    // Extract option index from the ID
                    const optionIndexMatch = activeDescendantId.match(/option-(\d+)$/);
                    if (optionIndexMatch && optionIndexMatch[1]) {
                        const optionIndex = parseInt(optionIndexMatch[1]);
                        // Get filtered options based on current input
                        const inputValue = field.value || '';
                        const filteredOptions = deliveryAddressData.filter(option => {
                            return filterFields.some(filterField => {
                                const fieldValue = option?.[filterField];
                                return fieldValue?.toLowerCase().includes(inputValue.toLowerCase());
                            });
                        });
                        
                        // Select the highlighted option
                        if (optionIndex >= 0 && optionIndex < filteredOptions.length) {
                            const selectedOption = filteredOptions[optionIndex];
                            // Auto-populate all shipping details fields
                            setValue('shipping_details.line1', selectedOption.line1);
                            setValue('shipping_details.line2', selectedOption.line2 || '');
                            setValue('shipping_details.city', selectedOption.city);
                            setValue('shipping_details.state_id', selectedOption.state_id);
                            setValue('shipping_details.zip', selectedOption.zip);
                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                            setStateDropDownValue(stateName);
                            setIsCreatePoDirty(true);
                            saveUserActivity();
                            saveBomHeaderDetails();
                            setOpenState(false);
                            
                            // Focus on next field after selection
                            setTimeout(() => {
                                const nextInput = document.querySelector(nextFieldSelector);
                                if (nextInput instanceof HTMLElement) {
                                    nextInput.focus();
                                }
                            }, 100);
                        }
                    }
                }
            }
        }
    };

    const handlePriceIntegration = async (index?: number) => {
        const totalWeight = calculateTotalWeightForProduct(watch(`cart_items`));
        setValue('totalWeight', totalWeight); 
        const productIdList = [];
        if(index){
            productIdList.push(watch(`cart_items.${index}.descriptionObj.Product_ID`)); 
        }
        else{
            for (const item of watch('cart_items')) {
                if (item?.descriptionObj?.Product_ID) {
                    productIdList.push(item.descriptionObj.Product_ID);
                }
            }
        }
        const searchZipCode = watch(`shipping_details.zip`) 
        if(productIdList.length === 0) return {};
        let netTotalWeight = totalWeight;
        if(netTotalWeight <= 500){
            netTotalWeight = 500;
        }
        const productPricesData = await getPriceExample(productIdList, searchZipCode, Math.floor(netTotalWeight));
        if(index){
            updateValue(index, productPricesData)
            saveUserLineActivity(sessionId, false, index);
        }else{
            for(let i = 0; i < watch('cart_items').length; i++){
                updateValue(i, productPricesData)
                if(watch(`cart_items.${i}.descriptionObj`)){
                    saveUserLineActivity(sessionId, false, i);
                }
            }
        }
        

    }

    const handleFileUpload = (event) => {
        const file = event.target.files[0];
        if (file) {
            if (!file.type.includes('pdf')) {
                showCommonDialog(null,"Please select a PDF file.", commomKeys.actionStatus.error, resetDialogStore, [ {name: commomKeys.errorBtnTitle, action: resetDialogStore}])
              return;
            }
            navigate(routes.bomExtractor, { state: { file } });
        }
    };

    const handleFileInputClick = () => {
        fileInputRef.current?.click();
        resetDialogStore();
    }

    const handleUploadClick = () => {
        if (orderInfoIsFilled) {
            const { delivery_date, shipping_details, order_type, internal_po_number, delivery_date_offset } = getValues();
            const formattedCreatePoData = {
                delivery_date,
                shipping_details,
                order_type,
                internal_po_number,
                delivery_date_offset
            }
            // setCreatePoData(formattedCreatePoData)
            setUploadBomInitialData(formattedCreatePoData)
            const isDirty = watch('cart_items')?.some(item => item.descriptionObj && Object.keys(item.descriptionObj).length > 0);
            const message = isCreatePOModule
                ? navigationConfirmMessages.unsavedChanges
                :
                navigationConfirmMessages.confirmLeave;
            if(isDirty){
                showCommonDialog(null,message, null, resetDialogStore, [ {name: 'Yes', action: handleFileInputClick}, {name: 'No', action: resetDialogStore}])
            }
            else{
                handleFileInputClick();
            }
        }
    };

    return (
        <>
            <div className={clsx(styles.createPoContent, 'bgBlurContent')}>
                <div className={styles.formInnerContent} ref={HeaderDetailsConfirmedRef}>
                            {/* <div className={styles.headerNoteCreatePO}>
                                <span className={styles.leftIcon}><WarningIcon /></span>

                                <span className={styles.headerNoteText}>All delivered material will be new (aka "prime"), fulfilled to the defined specification,
                                    loaded/packaged for forklift and/or magnetic offloading and have mill test reports.</span>
                                <span className={clsx(styles.headerNoteText, styles.marginTop8)}>The maximum bundle weight is 5,000 pounds.</span>
                                <span className={styles.rightIcon}><WarningIcon /></span>
                            </div> */}
                            <div className={clsx(styles.tblWrapper,'w100')}>
                                {
                                    (isSavedBom && createPoDataFromSavedBom) ? <SavedBomPreview watch={watch} fields={fields} getValues={getValues} calculateMaterialTotalPrice={calculateMaterialTotalPrice} /> : (
                                        <PseudoScroll 
                                    width="100%" 
                                    height="100%"
                                    scrollPosition={scrollPosition}
                                    maxScrollHeight={maxScrollHeight}
                                    onScrollChange={handleScrollChange}
                                    thumbColor={'#9da2b2'}
                                    thumbWidth={8}
                                    setIsDraggingScroll={setIsDraggingScroll}
                                    thumbMarginTop={20}
                                    thumbMarginBottom={isCreatePOModule ? 32 : 0}
                                    scrollThumbHeight={scrollThumbHeight}

                                >
                                    <>
                                        <div className={clsx(styles.headerContainer)} ref={headerContainerRef} onClick={scrollToTop} >
                                            <div className={styles.headerItem}>
                                                {watch('internal_po_number')?.toUpperCase() || '-'}
                                            </div>
                                            <div className={styles.headerItem}>
                                                {watch('delivery_date') ? 
                                                    `${dayjs(watch('delivery_date')).format('ddd').toUpperCase()}, ${watch('delivery_date')}` 
                                                    : '-'
                                                }
                                            </div>
                                            <div className={styles.headerItem}>
                                                {watch('order_type')?.toUpperCase() || '-'} 
                                            </div>
                                            <div className={styles.headerItem}>
                                            {<><span>{(watch('shipping_details.line1')?.toUpperCase() || '-')}</span> <span>{(watch('shipping_details.line2')?.toUpperCase() || '')}</span></>}
                                            </div>
                                        </div>
                                        <div className={clsx(styles.createPOContainer, !isCreatePOModule && styles.removeFooter)} ref={createPoContainerRef} onScroll={handleMainScroll}>
                                            <div className={clsx(styles.formInputGroup, isCalendarOpen && styles.isCalendarOpenDiabledInput )} ref={formInputGroupRef}
                                                data-hover-video-id='create-po-header' 
                                            >
                                            {isCalendarOpen && <div className={ styles.calendarOpenOverlay}></div>}

                                                <div className={styles.formInputGroup1}>
                                                    <InputWrapper>
                                                        <CustomTextField className={clsx(styles.inputfiled,styles.pOInput)} type='text' register={register("internal_po_number")}
                                                            placeholder='JOB / PO#'
                                                            onBlur={(e) => {
                                                                e.target.value = e.target.value.trim();
                                                                register("internal_po_number").onBlur(e);
                                                                saveUserActivity();
                                                                saveBomHeaderDetails();
                                                            }}
                                                            onChange={(e) => {
                                                                setIsCreatePoDirty(true)
                                                            }}
                                                            maxLength={20}
                                                            tabIndex={13}
                                                            inputRef={handleJobPoInputRef}
                                                            autoFocus={location.pathname !== routes.savedBom}
                                                        />
                                                    </InputWrapper>
                                                    <div className={styles.deliverByContainer}>
                                                        <Calendar allowedDates={allowedDates} 
                                                        value={watch('delivery_date')} 
                                                        setValue={setValue} 
                                                        isCalendarOpen={isCalendarOpen} 
                                                        setIsCalendarOpen={setIsCalendarOpen} 
                                                        disableDeliveryDate={disableDeliveryDate} 
                                                        handleOpenCalendar={handleOpenCalendarBtn}
                                                        saveUserActivity={saveUserActivity}
                                                        onDateSelect={handleDateSelect}
                                                        saveBomHeaderDetails={saveBomHeaderDetails}
                                                        />
                                                    </div> 
                                                    <div className={styles.radioGroupContainer}>
                                                        <span className={styles.chosseOneIcon}><ChooseOneIcon /></span>
                                                        <label
                                                            tabIndex={15}
                                                            className={clsx(`${styles.radioButtonLeft} ${(watch('order_type') === "BID") ? clsx(styles.selected,styles.bidSelected) : ""}`, styles.radioButton, disableBidBuyNow && styles.disableBidBuyNowBtn)}
                                                            onClick={() => {
                                                                if(!disableBidBuyNow){
                                                                    setValue('order_type', 'BID')
                                                                    setIsCreatePoDirty(true)
                                                                }
                                                            }}
                                                            onKeyDown={(event) => {
                                                                if (event.key === 'Enter' && !disableBidBuyNow) {
                                                                    setValue('order_type', 'BID');
                                                                    setIsCreatePoDirty(true)
                                                                }
                                                            }}
                                                        >
                                                            <input 
                                                                type="radio" 
                                                                {...register("order_type")} 
                                                                value="BID" 
                                                                className={styles.hiddenRadio}
                                                                onChange={(e) => {
                                                                    register('order_type').onChange(e)
                                                                    setIsCreatePoDirty(true)
                                                                    saveUserActivity()
                                                                    saveBomHeaderDetails()
                                                                }}
                                                                disabled={disableBidBuyNow}
                                                            />
                                                            BID
                                                        </label>
                                                        <label
                                                            tabIndex={16}
                                                            className={clsx(`${styles.radioButtonRight} ${(watch('order_type') === "BUY") ? clsx(styles.selected,styles.buySelected) : ""}`, styles.radioButton, disableBidBuyNow && styles.disableBidBuyNowBtn)}
                                                            onClick={() => {
                                                                if(!disableBidBuyNow){
                                                                    setValue('order_type', 'BUY')
                                                                }
                                                            }}
                                                            onKeyDown={(event) => {
                                                                if (event.key === 'Enter' && !disableBidBuyNow) {
                                                                    setValue('order_type', 'BUY');
                                                                }
                                                            }}
                                                        >
                                                            <input 
                                                                type="radio" 
                                                                {...register("order_type")} 
                                                                value="BUY" 
                                                                className={styles.hiddenRadio}
                                                                onChange={(e) => {
                                                                    register('order_type').onChange(e)
                                                                    saveUserActivity()
                                                                    saveBomHeaderDetails()
                                                                    setIsCreatePoDirty(true)
                                                                }}
                                                                disabled={disableBidBuyNow}
                                                            />
                                                            BUY
                                                        </label>
                                                    </div>
                                                </div>
                                                <div className={styles.formInputGroup1}>
                                                    <ClickAwayListener onClickAway={() => handleDeliveryInfoContainerClickAway()}>
                                                        <div 
                                                        className={`${styles.deliverToContainer} ${isFocused ? styles.boxShadow : ""}`} 
                                                        ref={containerRef} 
                                                        onClick={handleDeliveryToClick} 
                                                        tabIndex={17}
                                                        onFocus={()=>{
                                                            setOpenDeliveryToDialog(true);
                                                            setIsFocused(true)
                                                        }}
                                                        >
                                                            {openDeliveryToDialog ? 
                                                                <span className={styles.deliverToLabel}>
                                                                        <>
                                                                        <Controller
                                                                            name="shipping_details.line1"
                                                                            control={control}
                                                                            render={({ field }) => (
                                                                                <Autocomplete
                                                                                disableClearable
                                                                                className={clsx(styles.autocompleteContainer, styles.line1Input)}

                                                                                    options={deliveryAddressData || []}
                                                                                    value={null}
                                                                                    inputValue={field.value || ''}
                                                                                    open={autocompleteOpen && (field.value?.length || 0) > 0}
                                                                                    onOpen={() => {
                                                                                        if ((field.value?.length || 0) > 0) {
                                                                                            setAutocompleteOpen(true);
                                                                                        }
                                                                                    }}
                                                                                    onClose={() => setAutocompleteOpen(false)}
                                                                                    getOptionLabel={(option) => {
                                                                                        if (typeof option === 'string') return option;
                                                                                        if (!option) return '';
                                                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                                    }}
                                                                                    isOptionEqualToValue={(option, value) => {
                                                                                        return option?.id === value?.id;
                                                                                    }}
                                                                                    filterOptions={(options, { inputValue }) => {
                                                                                        if (!inputValue) return options;
                                                                                        const filtered = options.filter(option =>
                                                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                                                        );
                                                                                        return filtered;
                                                                                    }}
                                                                                    classes={{
                                                                                        paper: styles.autocompleteDropdown
                                                                                      }}
                                                                                    onInputChange={(event, newInputValue) => {
                                                                                        field.onChange(newInputValue);
                                                                                        setIsCreatePoDirty(true);
                                                                                        // Control when dropdown opens based on input length
                                                                                        if (newInputValue.length > 0) {
                                                                                            setAutocompleteOpen(true);
                                                                                        } else {
                                                                                            setAutocompleteOpen(false);
                                                                                        }
                                                                                    }}
                                                                                    onChange={(event, selectedOption) => {
                                                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                                                            // Auto-populate all shipping details fields
                                                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                                            setValue('shipping_details.city', selectedOption.city);
                                                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                                                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                                            setStateDropDownValue(stateName)
                                                                                            setIsCreatePoDirty(true);
                                                                                            saveUserActivity();
                                                                                            saveBomHeaderDetails();
                                                                                        }
                                                                                    }}
                                                                                    renderInput={(params) => (
                                                                                        <TextField
                                                                                            autoFocus
                                                                                            {...params}
                                                                                            className={clsx(styles.companyNameInput, styles.muiAutocompleteTextField)}
                                                                                            type='text'
                                                                                            placeholder='ADDRESS 1'
                                                                                            onBlur={(e) => {
                                                                                                e.target.value = e.target.value.trim();
                                                                                                field.onBlur();
                                                                                                saveUserActivity();
                                                                                                saveBomHeaderDetails();
                                                                                            }}
                                                                                            onKeyDown={(e) => {
                                                                                                if (e.key === 'Tab') {
                                                                                                    if (e.shiftKey) {
                                                                                                        const nextElement = document.querySelector('[tabindex="16"]');
                                                                                                        if (nextElement instanceof HTMLElement) {
                                                                                                            nextElement.focus();
                                                                                                        }
                                                                                                        setOpenDeliveryToDialog(false);
                                                                                                        setIsFocused(false)
                                                                                                    } else {
                                                                                                        handleAutocompleteTabSelection(
                                                                                                            e, 
                                                                                                            field, 
                                                                                                            autocompleteOpen, 
                                                                                                            ['line1', 'line2', 'city'], 
                                                                                                            setAutocompleteOpen, 
                                                                                                            'input[name="shipping_details.line2"]'
                                                                                                        );
                                                                                                    }
                                                                                                }
                                                                                            }}
                                                                                        />
                                                                                    )}
                                                                                    renderOption={(props, option) => (
                                                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                                            <div>
                                                                                            {formatAddressDisplay(option)}
                                                                                            </div>
                                                                                        </li>
                                                                                    )}
                                                                                    freeSolo
                                                                                    clearOnEscape
                                                                                    noOptionsText=""
                                                                                />
                                                                            )}
                                                                        />
                                                                                <Controller
                                                                                    name="shipping_details.line2"
                                                                                    control={control}
                                                                                    render={({ field }) => (
                                                                                        <Autocomplete
                                                                                        disableClearable
                                                                                            options={deliveryAddressData || []}
                                                                                            value={null}
                                                                                            inputValue={field.value || ''}
                                                                                            open={autocompleteOpenLine2 && (field.value?.length || 0) > 0}
                                                                                            onOpen={() => {
                                                                                                if ((field.value?.length || 0) > 0) {
                                                                                                    setAutocompleteOpenLine2(true);
                                                                                                }
                                                                                            }}
                                                                                            onClose={() => setAutocompleteOpenLine2(false)}
                                                                                            getOptionLabel={(option) => {
                                                                                                if (typeof option === 'string') return option;
                                                                                                if (!option) return '';
                                                                                                return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                                            }}
                                                                                            isOptionEqualToValue={(option, value) => {
                                                                                                return option?.id === value?.id;
                                                                                            }}
                                                                                            filterOptions={(options, { inputValue }) => {
                                                                                                if (!inputValue) return options;
                                                                                                return options.filter(option =>
                                                                                                    option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                                    option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                                    option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                                                                );
                                                                                            }}
                                                                                            onInputChange={(event, newInputValue) => {
                                                                                                field.onChange(newInputValue);
                                                                                                setIsCreatePoDirty(true);
                                                                                                // Control when dropdown opens based on input length
                                                                                                if (newInputValue.length > 0) {
                                                                                                    setAutocompleteOpenLine2(true);
                                                                                                } else {
                                                                                                    setAutocompleteOpenLine2(false);
                                                                                                }
                                                                                            }}
                                                                                            onChange={(event, selectedOption) => {
                                                                                                if (selectedOption && typeof selectedOption === 'object') {
                                                                                                    // Auto-populate all shipping details fields
                                                                                                    setValue('shipping_details.line1', selectedOption.line1);
                                                                                                    setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                                                    setValue('shipping_details.city', selectedOption.city);
                                                                                                    setValue('shipping_details.state_id', selectedOption.state_id);
                                                                                                    setValue('shipping_details.zip', selectedOption.zip);
                                                                                                    const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                                                    setStateDropDownValue(stateName);
                                                                                                    setIsCreatePoDirty(true);
                                                                                                    saveUserActivity();
                                                                                                    saveBomHeaderDetails();
                                                                                                }
                                                                                            }}
                                                                                            classes={{
                                                                                                paper: styles.autocompleteDropdown
                                                                                              }}
                                                                                            renderInput={(params) => (
                                                                                                <TextField 
                                                                                                    {...params}
                                                                                                    className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)} 
                                                                                                    type='text' 
                                                                                                    placeholder='ADDRESS 2'
                                                                                                    onBlur={(e) => {
                                                                                                        e.target.value = e.target.value.trim();
                                                                                                        field.onBlur();
                                                                                                        saveUserActivity();
                                                                                                        saveBomHeaderDetails();
                                                                                                    }}
                                                                                                    onKeyDown={(e) => {
                                                                                                        handleAutocompleteTabSelection(
                                                                                                            e, 
                                                                                                            field, 
                                                                                                            autocompleteOpenLine2, 
                                                                                                            ['line2', 'line1', 'city'], 
                                                                                                            setAutocompleteOpenLine2, 
                                                                                                            'input[name="shipping_details.city"]'
                                                                                                        );
                                                                                                    }}
                                                                                                />
                                                                                            )}
                                                                                            renderOption={(props, option) => (
                                                                                                <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                                                   <div>
                                                                                            {formatAddressDisplay(option)}
                                                                                            </div>
                                                                                                </li>
                                                                                            )}
                                                                                            freeSolo
                                                                                            clearOnEscape
                                                                                            noOptionsText=""
                                                                                        />
                                                                                    )}
                                                                                />
                                                                        </>
                                                                    <span className={styles.lastAddressFiled}>
                                                                        <span className={styles.addressInputsCol1} >
                                                                        <Controller
                                                                            name="shipping_details.city"
                                                                            control={control}
                                                                            render={({ field }) => (
                                                                                <Autocomplete
                                                                                disableClearable
                                                                                className={styles.autocompleteContainer}
                                                                                    options={deliveryAddressData || []}
                                                                                    value={null}
                                                                                    inputValue={field.value || ''}
                                                                                    open={autocompleteOpenCity && (field.value?.length || 0) > 0}
                                                                                    onOpen={() => {
                                                                                        if ((field.value?.length || 0) > 0) {
                                                                                            setAutocompleteOpenCity(true);
                                                                                        }
                                                                                    }}
                                                                                    onClose={() => setAutocompleteOpenCity(false)}
                                                                                    getOptionLabel={(option) => {
                                                                                        if (typeof option === 'string') return option;
                                                                                        if (!option) return '';
                                                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                                                    }}
                                                                                    isOptionEqualToValue={(option, value) => {
                                                                                        return option?.id === value?.id;
                                                                                    }}
                                                                                    filterOptions={(options, { inputValue }) => {
                                                                                        if (!inputValue) return options;
                                                                                        return options.filter(option =>
                                                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase())
                                                                                        );
                                                                                    }}
                                                                                    classes={{
                                                                                        paper: styles.autocompleteDropdown
                                                                                      }}
                                                                                    onInputChange={(event, newInputValue) => {
                                                                                        field.onChange(newInputValue);
                                                                                        setIsCreatePoDirty(true);
                                                                                        // Control when dropdown opens based on input length
                                                                                        if (newInputValue.length > 0) {
                                                                                            setAutocompleteOpenCity(true);
                                                                                        } else {
                                                                                            setAutocompleteOpenCity(false);
                                                                                        }
                                                                                    }}
                                                                                    onChange={(event, selectedOption) => {
                                                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                                                            // Auto-populate all shipping details fields
                                                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                                            setValue('shipping_details.city', selectedOption.city);
                                                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                                                            const stateName = referenceData?.ref_states?.find(state => state.id === selectedOption?.state_id)?.code || '';
                                                                                            setStateDropDownValue(stateName);
                                                                                            setIsCreatePoDirty(true);
                                                                                            saveUserActivity();
                                                                                            saveBomHeaderDetails();
                                                                                        }
                                                                                    }}
                                                                                    renderInput={(params) => (
                                                                                        <TextField 
                                                                                            {...params}
                                                                                            className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)} 
                                                                                            type='text' 
                                                                                            placeholder='CITY'
                                                                                            onBlur={(e) => {
                                                                                                e.target.value = e.target.value.trim();
                                                                                                field.onBlur();
                                                                                                saveUserActivity();
                                                                                                saveBomHeaderDetails();
                                                                                            }}
                                                                                            onKeyDown={(e) => {
                                                                                                handleAutocompleteTabSelection(
                                                                                                    e, 
                                                                                                    field, 
                                                                                                    autocompleteOpenCity, 
                                                                                                    ['city', 'line1', 'line2'], 
                                                                                                    setAutocompleteOpenCity, 
                                                                                                    'input[name="shipping_details.zip"]'
                                                                                                );
                                                                                            }}
                                                                                        />
                                                                                    )}
                                                                                    renderOption={(props, option) => (
                                                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                                            <div>
                                                                                            {formatAddressDisplay(option)}
                                                                                            </div>
                                                                                        </li>
                                                                                    )}
                                                                                    freeSolo
                                                                                    clearOnEscape
                                                                                    noOptionsText=""
                                                                                />
                                                                            )}
                                                                        />
                                                                        </span>
                                                                    
                                                                        <span className={clsx(styles.addressInputsCol2,errors?.shipping_details?.state_id?.message && styles.errorInput,
                                                                            stateInputFocus && styles.selectShade)}>
                                                                    { stateInputFocus && <>
                                                                        <div className={styles.shape1}></div>
                                                                        <div className={styles.shape2}></div>
                                                                    </>
                                                                    
                                                                        }
                                                                    <StateDropDown
                                                                        states={states}
                                                                        setValue={setValue}
                                                                        stateDropDownValue={stateDropDownValue}
                                                                        setStateDropDownValue={setStateDropDownValue}
                                                                        setStateInputFocus={setStateInputFocus}
                                                                        stateInputFocus={stateInputFocus}
                                                                    />
                                                                        </span>
                                                                        <span className={styles.addressInputsCol3}>
                                                                        <Tooltip
                                                                        title={errors?.shipping_details?.zip?.message}
                                                                        arrow
                                                                        placement={"top-end"}
                                                                        disableInteractive
                                                                        TransitionComponent={Fade}
                                                                        TransitionProps={{ timeout: 200 }}
                                                                        classes={{
                                                                            tooltip: 'stateTooltip',
                                                                        }}
                                                                    >
                                                                        <div>
                                                                            <InputWrapper>
                                                                                <CustomTextField className={clsx(styles.addressInputs, errors?.shipping_details?.zip?.message && styles.errorInput)} type='text'
                                                                                    register={register("shipping_details.zip")}
                                                                                    placeholder='ZIP'
                                                                                    onChange={(e) => {
                                                                                        register("shipping_details.zip").onChange(e);
                                                                                        const zipCode = e.target.value.replace(/\D/g, '');
                                                                                        setValue("shipping_details.zip", zipCode);
                                                                                    }}
                                                                                    maxLength={5}
                                                                                    onBlur={(e) => {
                                                                                        e.target.value = e.target.value.trim();
                                                                                        register("shipping_details.zip").onBlur(e);
                                                                                        saveUserActivity();
                                                                                        saveBomHeaderDetails();
                                                                                    }}
                                                                                    onKeyDown={(e)=>{
                                                                                        if(e.key === 'Tab'){
                                                                                            if(!e.shiftKey){
                                                                                                e.stopPropagation();
                                                                                                e.preventDefault();
                                                                                                const nextElement = document.querySelector('[tabindex="18"]');
                                                                                                if (nextElement instanceof HTMLElement) {
                                                                                                    nextElement.focus();
                                                                                                }
                                                                                                handleDeliveryInfoContainerClickAway()
                                                                                            }
                                                                                        }
                                                                                    }}
                                                                                    errorInput={errors?.shipping_details?.zip?.message}
                                                                                />
                                                                            </InputWrapper>
                                                                        </div>
                                                                    </Tooltip>
                                                                        </span>
                                                                    
                                                                    </span>
                                                                </span>
                                                            : 
                                                            <span className={styles.deliverToLabel}>
                                                                {
                                                                    (watch('shipping_details.line1') || watch('shipping_details.line2') || (watch('shipping_details.city') || stateDropDownValue || watch('shipping_details.zip'))) ? (  <>
                                                                            <p className={clsx(styles.addressInputs,styles.hideInputBackground)}>{watch('shipping_details.line1') ? `${watch('shipping_details.line1')}` : ''}</p>
                                                                            <p className={clsx(styles.addressInputs,styles.hideInputBackground)}>{watch('shipping_details.line2') ? `${watch('shipping_details.line2')}` : ''}</p>
                                                                            <span className={styles.lastAddressFiled}>
                                                                                <p className={clsx(styles.addressInputsCol1,styles.hideInputBackground)}>{watch('shipping_details.city') ? `${watch('shipping_details.city')}` : ''}</p>
                                                                                <p className={clsx(styles.addressInputsCol2,styles.hideInputBackground)}>{stateDropDownValue ? stateDropDownValue : ''}</p>
                                                                                <p className={clsx(styles.addressInputsCol3,styles.hideInputBackground)}>{watch('shipping_details.zip') ? `${watch('shipping_details.zip')}` : ''}</p>
                                                                            </span>
                                                                    </>):(<span>DELIVER TO</span>)
                                                                }
                                                            
                                                            </span>
                                                            }
                                                                
                                                        </div>
                                                    </ClickAwayListener>
                                                    <div className={`${styles.uploadBillContainer} ${!orderInfoIsFilled ? styles.disabled : ''}`} tabIndex={orderInfoIsFilled ? 18 : -1}
                                                        data-hover-video-id="upload-bom"
                                                        onFocus={() => {
                                                            scrollToTop();
                                                        }}

                                                        onKeyDown={(e) => {
                                                            if (e.key === 'Tab') {
                                                                if (!e.shiftKey) {
                                                                    if (orderInfoIsFilled) {
                                                                        e.stopPropagation();
                                                                        e.preventDefault();
                                                                        const descriptionInput = document.getElementById('combo-box-demo0');//document.querySelector('textarea[id="combo-box-demo"]');
                                                                        if (descriptionInput instanceof HTMLElement) {
                                                                            descriptionInput.focus();
                                                                        }
                                                                    }
                                                                } else {
                                                                    setOpenDeliveryToDialog(true);
                                                                    setIsFocused(true)
                                                                    setTimeout(() => {
                                                                        const descriptionInput = document.querySelector('input[name="shipping_details.line1"]');
                                                                        if (descriptionInput instanceof HTMLElement) {
                                                                            descriptionInput.focus();
                                                                        }
                                                                    }, 100)
                                                                }
                                                            }
                                                            if(e.key === 'Enter' || e.key === ' '){
                                                                handleUploadClick()
                                                            }
                                                        }}
                                                        onClick={handleUploadClick}
                                                    >
                                                        <input
                                                            type="file"
                                                            ref={fileInputRef}
                                                            onChange={handleFileUpload}
                                                            accept=".pdf,.doc,.docx,.xlsx,.xls"
                                                            style={{ display: 'none' }}
                                                        />
                                                        <div className={styles.uploadIcon}>
                                                            <UploadBOMIcon className={styles.uploadIcon1}/>
                                                            <UploadBOMIconHover className={styles.uploadIcon2}/>
                                                        </div>
                                                        <span className={styles.uploadLabel}>
                                                            UPLOAD BILL<br/>OF MATERIAL
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style={{overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden'}} className={clsx((!isCreatePOModule) ? styles.uploadBOMLineTable : styles.addPoLineTable , (!isCreatePOModule && filteredFields?.length === 3) && styles.uploadBOMLineTableMinHeight)} onScroll={handleLineItemScroll} ref={addPoLineTableRef} onClick={() => {openAddLineTab()}}>
                                                <table >
                                                    <thead>
                                                        {!isCreatePOModule ?
                                                            <tr>
                                                                <th><span>LN</span></th>
                                                                <th><span>DESCRIPTION</span></th>
                                                                <th><span>QTY</span></th>
                                                                <th colSpan={2}><span>LINE STATUS</span></th>
                                                            </tr>
                                                        :
                                                            <tr>
                                                                <th><span>LN</span></th>
                                                                <th><span>DESCRIPTION</span></th>
                                                                <th><span>QTY</span></th>
                                                                <th><span> $/UNIT</span></th>
                                                                <th colSpan={2}><span>EXT ($)</span></th>
                                                            </tr>
                                                        }
                                                    </thead>
                                                    <tbody>
                                                        {!isCreatePOModule ?
                                                            filteredFields.slice(startIndex, endIndex).map((item: any, _index: number) => {
                                                                const index =  (!isCreatePOModule) ? item.lineStatusIndex : startIndex + _index; 
                                                                viewLineStatusIndex++;
                                                                return (
                                                                <React.Fragment key={index}>
                                                                    {
                                                                        ((index) < bomUploadResult?.length) && <BomTile
                                                                            index={index}
                                                                            viewLineStatusIndex={viewLineStatusIndex}
                                                                            register={register}
                                                                            fields={fields}
                                                                            updateLineProduct={updateLineProduct}
                                                                            products={products}
                                                                            setValue={setValue}
                                                                            watch={watch}
                                                                            errors={errors}
                                                                            control={control}
                                                                            getValues={getValues}
                                                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                                                            removeLineItem={removeLineItem}
                                                                            userPartData={userPartData}
                                                                            sessionId={sessionId}
                                                                            selectedProduct={selectedProduct}
                                                                            searchStringData={searchStringData}
                                                                            setSearchString={setSearchString}
                                                                            setLineSessionId={setLineSessionId}
                                                                            lineSessionId={lineSessionId}
                                                                            handleCreatePOSearch={handleCreatePOSearch}
                                                                            apiCallInProgress={apiCallInProgress}
                                                                            quantitySizeValidator={quantitySizeValidator}
                                                                            saveUserLineActivity={saveUserLineActivity}
                                                                            orderInfoIsFilled={orderInfoIsFilled}
                                                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                                                            openAddLineTab={openAddLineTab}
                                                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                                                            hidePoLineScroll={hidePoLineScroll}
                                                                            setHidePoLineScroll={setHidePoLineScroll}
                                                                            scrollToTop={scrollToTop}
                                                                            bomTileDefaultData={bomUploadResult[index] ?? []}
                                                                            viewIndex={viewIndex}
                                                                            clearErrors={clearErrors}
                                                                            lastModifiedBomRef={lastModifiedBomRef}
                                                                            lastModifiedBom={lastModifiedBom}
                                                                            currentBomData={currentBomData}
                                                                            updateLineProductTag={updateLineProductTag}
                                                                            filterFieldsLength={filteredFields.length}
                                                                            filteredItemIndex={startIndex + _index}
                                                                            validateSavedBomCreatePo={validateSavedBomCreatePo}
                                                                        />
                                                                        
                                                                    }
                                                                </React.Fragment>
                                                            )
                                                        })
                                                        :
                                                        fields.map((item: any, index: number) => {
                                                            return (
                                                                <React.Fragment key={index}>
                                                                <CreatePoTile
                                                                            index={index}
                                                                            register={register}
                                                                            fields={fields}
                                                                            updateValue={updateValue}
                                                                            updateLineProduct={updateLineProduct}
                                                                            products={products}
                                                                            setValue={setValue}
                                                                            watch={watch}
                                                                            errors={errors}
                                                                            control={control}
                                                                            getValues={getValues}
                                                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                                                            removeLineItem={removeLineItem}
                                                                            userPartData={userPartData}
                                                                            sessionId={sessionId}
                                                                            selectedProduct={selectedProduct}
                                                                            searchStringData={searchStringData}
                                                                            setSearchString={setSearchString}
                                                                            setLineSessionId={setLineSessionId}
                                                                            lineSessionId={lineSessionId}
                                                                            handleCreatePOSearch={handleCreatePOSearch}
                                                                            apiCallInProgress={apiCallInProgress}
                                                                            quantitySizeValidator={quantitySizeValidator}
                                                                            saveUserLineActivity={saveUserLineActivity}
                                                                            orderInfoIsFilled={orderInfoIsFilled}
                                                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                                                            openAddLineTab={openAddLineTab}
                                                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                                                            hidePoLineScroll={hidePoLineScroll}
                                                                            setHidePoLineScroll={setHidePoLineScroll}
                                                                            scrollToTop={scrollToTop}
                                                                            calculateMaterialTotalPrice={calculateMaterialTotalPrice}
                                                                            setIsCreatePoDirty={setIsCreatePoDirty}
                                                                            isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                                                            cameFromSavedBom={cameFromSavedBom}
                                                                            handlePriceIntegration={handlePriceIntegration}
                                                                            clearErrors={clearErrors}
                                                                            updateLineItem={updateLineItem}
                                                                        />
                                                                </React.Fragment>
                                                            )
                                                        })
                                                    }
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            {/* <CommonTooltip
                                                    title={'Click this + to add more products to your purchase order'}
                                                    tooltiplabel={<>
                                                        <div className={styles.addMoreLine}>
                                                            <button onClick={() => { addNewRow(); }}>
                                                                <AddLineIcon className={styles.addLine} />
                                                                <AddLineHoverIcon className={styles.addLineHover} />
                                                            </button>
                                                            <span></span>
                                                        </div>
                                                    
                                                    </>
                                                        
                                                    }
                                                    placement={'bottom-start'}
                                                    classes={{
                                                        popper: 'tooltipPopper',
                                                        tooltip: 'tooltipMain tooltipSearch tooltipAddRow',
                                                        arrow: 'tooltipArrow'
                                                    }}
                                                    localStorageKey="addMoreLineTooltip"
                                                /> */}
                                            {/* <div className={styles.totalAmt}>
                                                <div className={styles.featureActions}>
                                                    <div>
                                                        {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&  <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyingPreference} disabled={disablePlaceOrderButton} getCartItems={getCartItems} />}
                                                    </div>
                                                    {buyingPreference?.bnpl_settings?.is_approved === null && <button className={styles.refreshNetTerm} onClick={initializeCreatePOData}><RefreshNet30Icon /><span>Click to check status (Net 30 Terms)</span></button>}
                                                </div>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td><span className={styles.saleTax}>Material Total</span></td>
                                                                <td><span className={styles.saleTax}>$</span></td>
                                                            <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`price`))}</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><span className={styles.saleTax}>
                                                                Sales Tax
                                                                <Tooltip
                                                                    title={SalesTaxTooltip()}
                                                                    arrow
                                                                    placement={'top-end'}
                                                                    disableInteractive
                                                                    TransitionComponent={Fade}
                                                                    TransitionProps={{ timeout: 200 }}
                                                                    classes={{
                                                                        tooltip: 'inputTooltip',
                                                                    }}
                                                                >
                                                                    <span className={styles.questionIcon}>
                                                                        <QuestionIcon className={styles.questionIcon1} />
                                                                        <QuestionHoverIcon className={styles.questionIcon2} />
                                                                    </span>

                                                                </Tooltip>

                                                            </span>
                                                            </td>
                                                            <td><span className={styles.saleTax}>$</span></td>
                                                            <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`sales_tax`))}</span></td>
                                                        </tr>
                                                        <tr className={clsx(watch('payment_method') !== 'ach_credit' && 'displayNone')}>
                                                            <td><span className={styles.saleTax}>Deposit</span></td>
                                                            <td><span className={styles.saleTax}>$</span></td>
                                                            <td><span className={styles.saleTax}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`depositAmount`))}</span></td>
                                                        </tr>
                                                        <tr>
                                                            <td><span className={styles.totalPurchase}>Total Purchase</span></td>
                                                            <td><span className={styles.totalPurchase}>$</span></td>
                                                            <td><span className={styles.totalPurchase}>{formatToTwoDecimalPlaces(!apiCallInProgress && watch(`totalPurchase`))}</span></td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div> */}
                                            {/* <div className={styles.btnOfCreatePo}>
                                                <div className={styles.selectedpayment}>

                                                <CommonTooltip
                                                    title={'Click this drop down box to select what method of payment you want to use for this order.'}
                                                    tooltiplabel={
                                                        <CustomMenu
                                                            control={control}
                                                            name={'payment_method'}
                                                            placeholder={'Method of Payment'}
                                                            className={styles.selectPaymentMethod}
                                                            MenuProps={{
                                                                classes: {
                                                                    paper: styles.selectPaymentMethodPaper,
                                                                },
                                                            }}
                                                            onOpen={() => setShowPaymentOptionTooltip(false)}
                                                            onClose={() => setShowPaymentOptionTooltip(undefined)}
                                                            items={paymentMethods}
                                                            IconComponent={DropdownIcon}
                                                            onChange={() => saveUserActivity()}
                                                        />
                                                    }
                                                    open={showPaymentOptionTooltip}
                                                    placement={'bottom-start'}
                                                    classes={{
                                                        popper: 'tooltipPopper',
                                                        tooltip: 'tooltipMain tooltipSearch tooltipPayment',
                                                        arrow: 'tooltipArrow'
                                                    }}
                                                    localStorageKey="selectPaymentTooltip"
                                                />

                                                </div>
                                                <button
                                                    onClick={handleSubmit(onSubmit)}
                                                    disabled={disablePlaceOrderButton}
                                                    className={styles.placeOrder}>Place Order</button>
                                            </div> */}
                                        </div>
                                    </>
                                </PseudoScroll>
                                    )
                                }
                                
                                {/* <div className={styles.textOfCreatePo}>
                                    <p>After clicking "Place Order," the next screen will be your order confirmation.</p>
                                    <p>Additionally, you will receive an email confirmation with full order details.</p>
                                </div> */}
                                {isCreatePOModule && <div className={styles.backBtnMain}>
                                    <button className={styles.cancelPOGoBack} onClick={() => handleNavigateAway(backNavigation)} >CANCEL</button>
                                    {(channelWindow?.fetchPdf || channelWindow?.generatePdf) &&  
                                        <PdfMakePage getExportPoData={getExportPoData} buyingPreferenceData={buyingPreference} disabled={disableFormValidation && location.pathname !== routes.savedBom} getCartItems={getCartItems} />
                                    }
                                    {!isSavedBom && <button className={styles.savePOGoBack} onClick={() => {
                                        showCommonDialog(null, 'This will exit Create PO. You can resume from your saved BOM. Do you want to continue?', null, resetDialogStore, [{ name: commomKeys.yes, action: handleSavePo },{ name: commomKeys.no, action: resetDialogStore }]);
                                    }} disabled={disableFormValidation} >SAVE PO / BUY LATER</button>}
                                </div>}
                            </div>
                        <Dialog
                            open={openErrorDialog}
                            onClose={(event) => setOpenErrorDialog(false)}
                            transitionDuration={200}
                            hideBackdrop
                            disableScrollLock={true}
                            container={HeaderDetailsConfirmedRef.current}
                            classes={{
                                root: styles.ErrorDialog,
                                paper: styles.dialogContent
                            }}

                        >
                            <p>{errorMessage}</p>
                            <button className={styles.submitBtn} onClick={(event) => {setOpenErrorDialog(false);}}>Ok</button>
                        </Dialog>

                    <Dialog
                         open={!isHeaderDetailsConfirmed && cameFromSavedBom}
                        transitionDuration={200}
                        disableScrollLock={true}
                        container={HeaderDetailsConfirmedRef.current}
                        style={{
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            border: '1px solid transparent',
                            borderRadius: '0px 0px 20px 20px',
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0
                    }
                }}
                hideBackdrop
                        classes={{
                            root: styles.confirmHeaderDetailsPopup,
                            paper: styles.dialogContent
                        }}

                    >

                        <div className={styles.confirmHeaderDetailsContainer}>
                            <span>CONFIRM HEADER DETAILS</span>
                            <button onClick={() => {
                                setIsHeaderDetailsConfirmed(true);
                            }}>PROCEED</button>
                        </div>


                    </Dialog>

                </div>
            </div>
        </>
    );
}


const CreatePo = () => {
    const navigate = useNavigate();
    const location = useLocation(); // Add this line to get location

    const navigateToSettings = () => {
        navigate(routes.buyerSettingPage , {state: {tab: 'PAYMENTS'}})
    }

    return (
        // Add key prop using pathname to force remount when path changes
        <InstantPurchasingWrapper
            key={location.pathname}
            navigateToSettings={navigateToSettings}
        >
            <InstantPurchasing navigateToSettings={navigateToSettings} />
        </InstantPurchasingWrapper>
    )
}

export default CreatePo;
