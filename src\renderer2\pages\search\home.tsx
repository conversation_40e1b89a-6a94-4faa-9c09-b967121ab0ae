import { useEffect, useState, useRef } from 'react';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { useSellerOrderStore, useGlobalStore, selectProduct, dataOpticsApi1, dataOpticsApi2, userRole, referenceProductItem, getFormattedUnit, useBuyerSettingStore, priceUnits, useSearchStore, commomKeys } from '@bryzos/giss-ui-library';
import { ProductPricingModel, SearchAnalyticDataModel, HttpRequestPayload } from '../../types/Search';
import styles from './home.module.scss'
import clsx from 'clsx';
import SearchHeader from '../SearchHeader';
import SearchLeftSection from './searchLeftSection/SearchLeftSection';
import SearchResultPanel from './mainContent/SearchResultPanel';
import SelectedProductList from './mainContent/SelectedProductList';
import SearchRightSection from './searchRightSection/SearchRightSection';
import MainContent from './mainContent/MainContent';
import { getPriceExample } from 'src/renderer2/utility/priceIntegratorExample';
import ProductSearch from './headerSection/productSearch';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import { fetchPrice, getLocal, handleSaveSearchProducts, newPriceFormatter, setLocal, updateSelectedPriceSearchData } from 'src/renderer2/helper';
import { localStorageKeys } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import usePostSaveSearchProducts from 'src/renderer2/hooks/usePostSaveSearchProducts';

const Home = () => {
    const { enableShareWidget, setEnableShareWidget, setShowLoader, setSearchSessionId, searchSessionId, userData, discountData, productMapping, referenceDataUpdated }: any = useGlobalStore();

    const { shortListedSearchProductsData, setShortListedSearchProductsData, searchByProductResult, selectedProductsData, setSelectedProductsData, selectedPriceUnit, selectedDomesticOption, setFilterShortListedSearchProductsData, setFilterSearchByProductResult, sessionId, setSessionId, searchZipCode, orderSizeSliderValue, resetSearchStore } = useSearchStore();
    const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
    const setSelectedSavedSearch = useSearchStore(state => state.setSelectedSavedSearch);
    const updatedSavedSearchDataFromSocket = useSearchStore(state => state.updatedSavedSearchDataFromSocket);
    const removedSavedSearchDataFromSocket = useSearchStore(state => state.removedSavedSearchDataFromSocket);
    const updatedSavedSearchList = useSearchStore(state => state.updatedSavedSearchList);
    const resetCurrentSearch = useSearchStore(state => state.resetCurrentSearch);
    const clickedCreateNewButton = useLeftPanelStore(state => state.clickedCreateNewButton);
    const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { mutateAsync: saveSearchProductsMutation } = usePostSaveSearchProducts();
    const [showWidgetPanel, setShowWidgetPanel] = useState<boolean>(false);
    const { resetFiltersInPurchaseOrders }: any = useSellerOrderStore();
    const analyticRef = useRef<string>();
    const [isShareWidget, setIsShareWidget] = useState<boolean>(false);
    analyticRef.current = sessionId;


    useEffect(() => {
        saveStoreLocalPriceSearchData();
        resetFiltersInPurchaseOrders();
        setShowLoader(false);
        if (searchSessionId) {
            setSessionId(searchSessionId)
        } else {
            const sessionId = uuidv4();
            setSessionId(sessionId);
        }

        return () => {
            setEnableShareWidget(false);
            const dataOpticsPayload = {
                "data": {
                    "session_id": analyticRef.current,
                    "move_to_screen": location.pathname.replace('/', "")
                }
            }
            dataOpticsApi2(dataOpticsPayload)
            // resetSearchStore();
            resetCurrentSearch();
            setSelectedSavedSearch(null);
            saveStoreLocalPriceSearchData();
        }
    }, []);

    useEffect(()=>{
        if(clickedCreateNewButton){
            setSelectedSavedSearch(null);
            resetCurrentSearch();
            saveStoreLocalPriceSearchData();
            setClickedCreateNewButton(null);
        }
    },[clickedCreateNewButton])

    const saveStoreLocalPriceSearchData = () => {
        const localPriceSearchData = getLocal(localStorageKeys.instantPriceSearch, null);
        if (localPriceSearchData && typeof localPriceSearchData === 'object' && 'title' in localPriceSearchData) {
            handleSaveSearchProducts(localPriceSearchData, saveSearchProductsMutation);
        }
    }

    useEffect(() => {
        if (enableShareWidget) {
            handleOpenWidget();
        }
        else {
            handleCloseWidget();
        }
    }, [enableShareWidget])

    useEffect(() => {
        if (sessionId) {
            setSearchSessionId(sessionId);
        }
    }, [sessionId])

    useEffect(() => {
        const processProducts = async () => {
            if (productMapping && shortListedSearchProductsData?.length) {
                fetchPrice(shortListedSearchProductsData, searchZipCode, orderSizeSliderValue);
            }
            else {
                setShortListedSearchProductsData([]);
            }
        };

        processProducts();
    }, [discountData, productMapping, referenceDataUpdated]);


    useEffect(() => {
        filterSearchByProductList();
        filterSelectedSearchProductList();
    }, [selectedDomesticOption, shortListedSearchProductsData]);

    useEffect(()=>{
        filterSavedSearchProductList();
    },[selectedDomesticOption])

    useEffect(() => {
        filterSearchByProductList()
    }, [searchByProductResult]);

    useEffect(() => {
        if (shortListedSearchProductsData.length > 0) {
            const updatePriceAndData = async () => {
                await fetchPrice(shortListedSearchProductsData, searchZipCode, orderSizeSliderValue);
                updateSelectedPriceSearchData(shortListedSearchProductsData);
            };

            updatePriceAndData();
        }
    }, [searchZipCode, orderSizeSliderValue])

    useEffect(()=>{
        if(shortListedSearchProductsData.length > 0){
            filterSavedSearchProductList();
        }
    },[selectedPriceUnit])

    useEffect(()=>{
        updateSelectedSavedSearchDataFromSocket();
    },[updatedSavedSearchDataFromSocket, updatedSavedSearchList])

    useEffect(()=>{
        if(removedSavedSearchDataFromSocket?.removed_ids && removedSavedSearchDataFromSocket?.removed_ids?.includes(selectedSavedSearch?.id)){
            setSelectedSavedSearch(null);
            setShortListedSearchProductsData([]);
            setFilterShortListedSearchProductsData([]);
            showCommonDialog(null, "Selected Price Search is Removed From the List", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
    },[removedSavedSearchDataFromSocket, selectedSavedSearch])

    const filterSearchByProductList = () => {
        if (searchByProductResult.length !== 0) {
            let filteredSearchByProductResult = [...searchByProductResult];
            if (selectedDomesticOption) {
                filteredSearchByProductResult = filteredSearchByProductResult.filter(product => product.domestic_material_only);
            }
            setFilterSearchByProductResult(filteredSearchByProductResult);
        } else {
            setFilterSearchByProductResult([]);
        }
    }

    const filterSelectedSearchProductList = () => {
        if (shortListedSearchProductsData.length !== 0) {
            let filteredSelectedProductSearchData = [...shortListedSearchProductsData];
            if (selectedDomesticOption) {
                filteredSelectedProductSearchData = shortListedSearchProductsData.filter((selectedProduct) => selectedProduct.domestic_material_only);
            }
            setFilterShortListedSearchProductsData(filteredSelectedProductSearchData);
        } else {
            setFilterShortListedSearchProductsData([]);
        }
    }

    const filterSavedSearchProductList = () => {
        if (selectedSavedSearch?.products?.length > 0) {
            let filteredSavedProductSearchData = shortListedSearchProductsData;
            if (selectedDomesticOption) {
                filteredSavedProductSearchData = filteredSavedProductSearchData.filter(product => product.domestic_material_only);
            }
            updateSelectedPriceSearchData(filteredSavedProductSearchData);
        }
    }

    const handleOpenWidget = () => {
        setShowWidgetPanel(true);
        setIsShareWidget(true);
    };
    const handleCloseWidget = () => {
        setShowWidgetPanel(false);
        setIsShareWidget(false);
        setEnableShareWidget(false);
    };

    const onShareProductPricing = async (emailTo: string, emailContent: string): Promise<void> => {
        const _selectedProduct: ProductPricingModel[] = selectedProductsData.length === 0 ? shortListedSearchProductsData : selectedProductsData;
        const productList: any[] = [];
        const dataOpticsData: SearchAnalyticDataModel[] = [];
        const { buyerSetting } = useBuyerSettingStore.getState();
        const defaultZipCode = buyerSetting?.price_search_zip || '63105';
        const _zipcode = searchZipCode?.length === 5 ? searchZipCode :  defaultZipCode;
        _selectedProduct.forEach((product: ProductPricingModel) => {
            productList.push({
                "product_id": product.id,
                "product_description": product.UI_Description,
                "price_ft": product.ft_price.trim().replace("$", ""),
                "price_lb": product.lb_price.trim().replace("$", ""),
                "price_cwt": product.cwt_price.trim().replace("$", ""),
                "price_share_type": selectedPriceUnit !== 'cwt,ft' ? getFormattedUnit(selectedPriceUnit)?.toLowerCase() : product.product_type_pipe ? "ft" : "cwt",
            });
            dataOpticsData.push({
                "session_id": sessionId,
                "line_session_id": product.line_session_id,
                "product_id": product.id,
                "description": product.UI_Description,
                "price_shared": true,
                "search_price_unit": selectedPriceUnit !== 'cwt,ft' ? getFormattedUnit(selectedPriceUnit)?.toLowerCase() : product.product_type_pipe ? "ft" : "cwt",
                "zip_code": _zipcode.trim(),
                "order_size": String(orderSizeSliderValue),
                "price" : {
                    "price_ft": product?.ft_price,
                    "price_lb": product?.lb_price,
                    "price_cwt": product?.cwt_price,
                    "price_pc": product?.pc_price,
                },
            })
        });

        const dataOpticsPayload: HttpRequestPayload<SearchAnalyticDataModel[]> = {
            "data": dataOpticsData
        }
        dataOpticsApi1(dataOpticsPayload)
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent.length === 0 ? null : emailContent,
                "products": productList,
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareProductPrice', payload)
            setSelectedProductsData([]);
            return res.data.data
        } catch (err) {
            throw new Error("Share Product Pricing Api Failure");
        }
    }

    const onShareApp = async (emailTo: string, emailContent: string): Promise<void> => {
        const payload = {
            data: {
                "user_id": userData.data.id,
                "from_email": userData.data.email_id,
                "to_email": emailTo,
                "email_content": emailContent.length === 0 ? null : emailContent
            }
        }
        try {
            const res = await axios.post(import.meta.env.VITE_API_SERVICE + '/user/shareWidgetRequest', payload)
            return res.data.data;
        } catch (err) {
            throw new Error("Share App Api Failure");
        }
    }

    const shareHandler = isShareWidget ? onShareApp : onShareProductPricing;

    const handleSharePrice = () => {
        setShowWidgetPanel(true);
    }

    const updateSelectedSavedSearchDataFromSocket = async() => {
        const updatedSavedSearchData = updatedSavedSearchDataFromSocket?.data?.id ? updatedSavedSearchDataFromSocket?.data : updatedSavedSearchList?.find((search: any) => search?.id === selectedSavedSearch?.id);
        if(updatedSavedSearchData?.id && updatedSavedSearchData?.id === selectedSavedSearch?.id){
            await fetchPrice(updatedSavedSearchData?.products, updatedSavedSearchData?.zipcode, parseFloat(updatedSavedSearchData?.order_size.replace(/[$,]/g, "")));
        }
    }

    return (
        <div className={styles.mainContent}>
            <ProductSearch />
            <div className={styles.innerContent}>
            {/* {!!selectedSavedSearch?.pricing_expired && <div className={styles.expiredSearchOverlay}></div>} */}
                {/* <SearchLeftSection /> */}
                <MainContent />
                {/* <SearchRightSection /> */}
            </div>
        </div>
    );
};

export default Home;

