import React, { useEffect, useState } from 'react';
import PricingBracket from './PricingBracket';
import styles from './OrderSummary.module.scss';
import clsx from 'clsx';
import { Fade, Tooltip } from '@mui/material';
import { ReactComponent as DropdownIcon } from '../../assets/New-images/StateIconDropDpown.svg';
import PlaceOrderDefault from '../../assets/New-images/Place_Order_Default.svg';
import PlaceOrderDefaultDisabled from '../../assets/New-images/Place_Order_Disabled.svg';
import PlaceOrderHover from '../../assets/New-images/Place_Order_Hover.svg';
import { dateTimeFormat, formatToTwoDecimalPlaces, orderType, purchaseOrder, useBuyerSettingStore, useCreatePoStore } from '@bryzos/giss-ui-library';
import { CustomMenu } from 'src/renderer2/pages/buyer/CustomMenu';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import { BNPLSTATUS, localStorageKeys, routes } from 'src/renderer2/common';
import { useLocation, useNavigate } from 'react-router-dom';
import { formatCurrency, getLocal } from 'src/renderer2/helper';
import { useLeftPanelStore } from '../LeftPanel/LeftPanelStore';
import dayjs from 'dayjs';
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

interface WeightRange {
  min: number;
  max: number;
  gear: number;
}
interface OrderSummaryProps {
  numberOfBrackets?: number;
  bracketDivider?: number;
  minWeight?: number;
  animationTime?: number;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ numberOfBrackets = 12, bracketDivider = 10, animationTime = 1000 }) => {
  const location = useLocation();
  const [weight, setWeight] = useState(0);
  const { setCreatePoData, setWeightGear, selectedQuote, setSelectedQuote, quoteList, setQuoteList, setIsCreatePoDirty } = useCreatePoStore();
  const setDisplayLeftPanel = useLeftPanelStore(state => state.setDisplayLeftPanel);
  const setIsConvertingToPo = useCreatePoStore(state => state.setIsConvertingToPo);
  const [totalNumberOfBarsFilled, setTotalNumberOfBarsFilled] = useState(0);
  const [weightRanges, setWeightRanges] = useState<any[]>([]);
  const [minWeight, setMinWeight] = useState(500);
  const [selectedGear, setSelectedGear] = useState(0);
  const { props } = useRightWindowStore();
  const { watch, control, paymentMethods, setValue, getValues, navigateToSettings, saveUserActivity, disablePlaceOrderButton, onSubmit, pricingBrackets, poHeaderFormWatch, disableConvertToPoButton, handleDraftPoSave, initialData } = props || {};
  // if(!watch) return null;
  const [numberOfGears, setNumberOfGears] = useState(6);
  const hasCreditLimit = Boolean(watch?.('bnplStatus') === BNPLSTATUS.ENABLED);
  const hasRequestedLimit = Boolean(watch?.('bnplStatus') === BNPLSTATUS.PENDING);
  const needsSetup = Boolean(watch?.('bnplStatus') === BNPLSTATUS.REJECTED);
  const isBnplHold = Boolean(watch?.('bnplStatus') === BNPLSTATUS.ON_HOLD);
  const isBnplRestricted = Boolean(watch?.('bnplStatus') === BNPLSTATUS.RESTRICTED);
  const buyerSetting = useBuyerSettingStore((state: any) => state.buyerSetting);
  const needsCardSetup = Boolean(!buyerSetting?.card && !buyerSetting?.card?.id);

  const navigate = useNavigate();
  const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);

  useEffect(() => {
    if (pricingBrackets?.length > 0) {
      const transformedRanges = (pricingBrackets).map((range: any, index: number) => ({
        min: Number(range.min_weight),
        max: Number(range.max_weight),
        gear: index + 1
      }));
      setMinWeight(transformedRanges[0]?.min);
      setWeightRanges(transformedRanges);
      setNumberOfGears(transformedRanges.length);
    }
  }, [pricingBrackets]);


  const calculateBracket = (weightRange: WeightRange, weight: number): void => {
    const totalNumberOfBars = numberOfBrackets * bracketDivider;
    const perBar = (weightRange.max - weightRange.min + 1) / totalNumberOfBars;
    const barToBeFilled = Math.ceil((weight - weightRange.min + 1) / perBar);
    let totalBars = (weightRange.gear - 1) * numberOfBrackets * bracketDivider + barToBeFilled;
    if (totalBars <= 0) {
      totalBars = 0;
    }
    if (totalBars >= (numberOfBrackets * bracketDivider * numberOfGears))
      totalBars = numberOfBrackets * bracketDivider * numberOfGears;
    setTotalNumberOfBarsFilled(totalBars);
  };

  const calculateGearPosition = (weight: number): void => {
    if (weightRanges.length > 0 && weight >= weightRanges[0]?.min) {
      const _weightRange = weightRanges?.find(range => weight >= range.min && weight <= range.max) ?? weightRanges[numberOfGears - 1];
      // setSelectedGear(_weightRange.gear-1);
      calculateBracket(_weightRange, weight);
    }
    else {
      // setSelectedGear(0);
      setTotalNumberOfBarsFilled(0);
    }
  };
  useEffect(() => {
    if (weightRanges.length > 0) {
      const weight = Math.floor(watch?.('total_weight'));
      setWeight(weight);
      calculateGearPosition(weight);
    }
  }, [watch?.('total_weight'), weightRanges]);

  useEffect(() => {
    setWeightGear(selectedGear === 0 ? 0 : selectedGear - 1);
  }, [selectedGear])

  // Generate bracket values with increasing increments

  const handlePlaceOrder = () => {
    onSubmit(watch?.());
    // const button = document.getElementById('place-order-btn');
    // if (button) {
    //   button.classList.add(styles.scale);
    //   setTimeout(() => {
    //     button.classList.remove(styles.scale);
    //     // Show success message or navigate to confirmation page
    //   }, 150);
    // }
  };

  const handleUpdatePricing = () => {
    setCreatePoData({ ...getValues(), cameFromSavedBom: true });
    setDisplayLeftPanel(true);
    navigate(routes.createPoPage);
  }

  const handleConvertToPo = async () => {
    setIsCreatePoDirty(false);
    const localQuote = getLocal(localStorageKeys.poQuoting, null);
    let localDraftData = localQuote || selectedQuote;
    localDraftData.order_type = orderType.PO;
    setIsConvertingToPo(localDraftData.id);
    await handleDraftPoSave(localDraftData, initialData, true);
    localDraftData.cameFromQuote = true;
    setSelectedQuote(localDraftData);
    setQuoteList(quoteList.filter((quote: any) => quote.id !== localDraftData.id));
    navigate(routes.createPoPage, { state: { from: 'quoting' } });
  }

  return (
    <div className={clsx(styles.container, 'orderSummaryContainer')} data-hover-video-id="pricing-bracket-po">
      <div className={styles.fadeIn}>
        <div>
          <PricingBracket
            currentWeight={weight}
            minWeight={minWeight}
            numberOfBrackets={numberOfBrackets}
            bracketDivider={bracketDivider}
            numberOfGears={numberOfGears}
            totalNumberOfBarsFilled={totalNumberOfBarsFilled}
            animationTime={animationTime}
            selectedGear={selectedGear}
            setSelectedGear={setSelectedGear}
          />
        </div>

        {/* Order Summary */}
        <div className={styles.slideUp} style={{ animationDelay: '100ms' }}>
          <div>
            <div className={styles.summarySection}>
              <div className={styles.summaryRow}>
                <div className={styles.summaryRowLbl}>Material Total</div>
                <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch?.(`buyer_po_price`))}</div>
              </div>
              <div className={`${styles.summaryRow} ${styles.muted}`}>
                <div className={styles.summaryRowLbl}>Sales Tax</div>
                <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch?.(`sales_tax`))}</div>
              </div>
              {watch?.('payment_method') === purchaseOrder.paymentMethodACH && (
                <div className={`${styles.summaryRow} ${styles.muted}`}>
                  <div className={styles.summaryRowLbl}>Deposit</div>
                  <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch?.(`deposit_amount`))}</div>
                </div>
              )}
              {watch?.('payment_method') === "card" && (
                <div className={`${styles.summaryRow} ${styles.muted}`}>
                  <div className={styles.summaryRowLbl}>Credit Card Fee</div>
                  <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch?.(`processing_fees`))}</div>
                </div>
              )}
              <div className={`${styles.summaryRow} ${styles.muted}`}>
                <div className={styles.summaryRowLbl}>Subscription</div>
                <div className={styles.summaryRowNum}>$ {formatToTwoDecimalPlaces(watch?.(`subscription`))}</div>
              </div>
            </div>
            <div className={clsx(styles.summaryRow, styles.totalPurchase)}>
              <div className={styles.totalPurchaseLbl}>Total Purchase</div>
              <div className={styles.totalPurchaseNum}>$ {formatToTwoDecimalPlaces(watch?.(`totalPurchase`))}</div>
            </div>
          </div>

          {poHeaderFormWatch?.('pricing_expired') === 1 &&
            <div className={styles.updatePricingNote}>
              First, click  <span>“Update Pricing”</span> button <br />to {poHeaderFormWatch?.('order_type') === orderType.QUOTE ? 'Convert to a PO' : 'place order'}.
            </div>
          }

        </div>
        {(location.pathname === routes.createPoPage && control) ? (
          <>
            {(!poHeaderFormWatch?.('pricing_expired') || poHeaderFormWatch?.('pricing_expired') === 0) &&
              <div className={styles.disclaimer}>
                If you make an error, you will have an opportunity<br />
                to change or cancel your order via “Order Management.”
              </div>
            }

            <Tooltip
              title={(hasCreditLimit && watch?.('totalPurchase') > watch?.('availableCreditLimit') && watch?.('payment_method') === purchaseOrder.paymentMethodBNPL) ? "Not enough available credit to complete this purchase" : ""}
              arrow
              placement="top"
              classes={{
                tooltip: styles.creditLimitTooltip,
                arrow: styles.tooltipArrow
              }}
            >
              <div>
                <CustomMenu
                  control={control}
                  name="payment_method"
                  placeholder="Method of Payment"
                  className={clsx((hasCreditLimit && watch?.('totalPurchase') > watch?.('availableCreditLimit') && watch?.('payment_method') === purchaseOrder.paymentMethodBNPL) ? 'netTermsDropdown net30TermError' : 'netTermsDropdown')}
                  MenuProps={{
                    TransitionComponent: Fade,
                    classes: {
                      paper: `${styles.dropdownList} ${styles.menuSlideUp}`,
                      list: styles.muiMenuList,
                    },
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left',
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left',
                    },
                    id: `payment-method-menu`
                  }}
                  disabled={poHeaderFormWatch?.('pricing_expired') === 1}
                  onKeyDown={(e: any) => {
                    if (e.key === 'Tab' && document.activeElement?.closest(`#payment-method-menu`)) { // Update selector to match new ID
                      const value = document.activeElement?.getAttribute('data-value');
                      if (value === purchaseOrder.paymentMethodBNPL) {
                        setValue('payment_method', purchaseOrder.paymentMethodBNPL);
                      } else if (value === "card") {
                        setValue('payment_method', "card");
                      } else {
                        setValue('payment_method', purchaseOrder.paymentMethodACH);
                      }
                      saveUserActivity();
                      setIsCreatePoDirty(true);
                      if(!orderInfoIsFilled){ 
                        const nextElement = document.querySelector('[tabindex="13"]');
                        if (nextElement instanceof HTMLElement) {
                          setTimeout(() => {
                            nextElement.focus();    
                          }, 300)
                        }
                      }
                    }
                  }}
                  items={paymentMethods}
                  IconComponent={DropdownIcon}
                  onChange={(e) => { saveUserActivity(); setValue('payment_method', e.target.value); setIsCreatePoDirty(true) }}
                  disableAutoFocusItem={watch?.('payment_method') ? false : true}
                  renderValue={(_value: string) => {
                    let value = _value || watch?.('payment_method');
                    const selectedItem = paymentMethods.find((item: { value: string; title: string }) => item.value === value);
                    const title = selectedItem?.title || value;

                    const totalPurchase = watch?.('totalPurchase');
                    const exceedsCreditLimit = totalPurchase > (watch?.('availableCreditLimit') || 0);

                    const handleMethodClick = () => {
                      if (needsSetup && value === purchaseOrder.paymentMethodBNPL) {
                        setValue('payment_method', purchaseOrder.paymentMethodBNPL);
                        setCreatePoData(getValues());
                        navigateToSettings();
                      }else if(needsCardSetup && value === purchaseOrder.paymentMethodCard){
                        setValue('payment_method', purchaseOrder.paymentMethodCard);
                        setCreatePoData(getValues());
                        navigateToSettings();
                      }
                    };

                    const getPaymentMethodText = () => {
                      if (!title) return 'Method of Payment';

                      if (value === purchaseOrder.paymentMethodBNPL) {
                        if (hasRequestedLimit) return `${title} (Pending)`;
                        if (needsSetup) return `${title} (Setup)`;
                        if (isBnplHold) return `${title} (On Hold)`;
                        if (isBnplRestricted) return `${title} (Restricted)`;
                      } else if(value === purchaseOrder.paymentMethodCard){
                        if(needsCardSetup) return `${title} (Setup)`;
                      }
                      return title;
                    };

                    return (
                      <div className={clsx(
                        styles.dropdownDataMain,
                        exceedsCreditLimit && value === purchaseOrder.paymentMethodBNPL && styles.exceedsCreditLimit
                      )}>
                        <span
                          className={clsx(styles.paymentMethod, 'paymentMethod')}
                          onClick={handleMethodClick}
                        >
                          {getPaymentMethodText()}
                        </span>

                        {((hasCreditLimit || hasRequestedLimit) && value === purchaseOrder.paymentMethodBNPL) && (
                          <span className={clsx(styles.paymentValue, 'paymentValue')}>
                            $ {formatToTwoDecimalPlaces(
                              hasCreditLimit
                                ? watch?.('availableCreditLimit')
                                : watch?.('requestedCreditLimit')
                            )}
                          </span>
                        )}
                        {
                          (isBnplRestricted && value === purchaseOrder.paymentMethodBNPL) && (
                            <span className={clsx(styles.paymentValue, 'paymentValue')}>
                              {formatCurrency(watch?.('max_restricted_amount'))}
                            </span>
                          )
                        }

                      </div>
                    );
                  }}
                />
              </div>
            </Tooltip>

            {/* Place Order Button */}
            <button
              id="place-order-btn"
              className={`${styles.acceptButton} ${styles.slideUp}`}
              style={{ animationDelay: '500ms' }}
              disabled={(disablePlaceOrderButton || poHeaderFormWatch?.('pricing_expired') === 1)}
              onClick={handlePlaceOrder}
            >
              <span>PLACE  ORDER</span>
            </button>
          </>
        ) : poHeaderFormWatch?.('order_type') === orderType.QUOTE && (
          <>
            <button
              id="place-order-btn"
              style={{ animationDelay: '500ms' }}
              className={styles.updatePricingButton}
              disabled={(disableConvertToPoButton || poHeaderFormWatch?.('pricing_expired') === 1)}
              onClick={handleConvertToPo}
            >
              CONVERT TO PO
            </button>
          </>
        )
          // : (
          //   <>
          //     <div className={styles.disclaimerSavedBom}>
          //  Pricing above reflects the quoted price from the date this draft was saved. Click below to generate today’s pricing in a new PO, as well as the ability to make material or quantity changes if necessary.
          // </div> 
          // <button
          //   id="place-order-btn"
          //   style={{ animationDelay: '500ms' }}
          //   className={styles.updatePricingButton}
          //   onClick={handleUpdatePricing}
          // >
          //   UPDATE PRICING
          // </button>
          //   </>
          // ) 
        }
      </div>
    </div>
  );
};

export default OrderSummary;
