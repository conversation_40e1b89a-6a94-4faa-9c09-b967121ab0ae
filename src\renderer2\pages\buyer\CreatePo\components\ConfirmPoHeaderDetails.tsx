import React, { useState, useRef, useEffect } from 'react';
import { Autocomplete, ClickAwayListener, IconButton, TextField, Tooltip } from '@mui/material';
import { Fade } from '@mui/material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/New-Image-latest/Close-Header-Info.svg' ;
import clsx from 'clsx';
import styles from '../CreatePo.module.scss';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import { getLocal } from 'src/renderer2/helper';
import { localStorageKeys } from 'src/renderer2/common';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import StateDropDown from 'src/renderer2/component/StateDropDown/StateDropDown';
import { commomKeys, dateTimeFormat, mobileDiaglogConst } from '@bryzos/giss-ui-library';
import dayjs from 'dayjs';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';

// Yup validation schema for the form
const confirmPoHeaderSchema = yup.object().shape({
    buyer_internal_po: yup.string().required('Job/PO is required').max(20, 'Job/PO must be 20 characters or less'),
    delivery_date: yup.string().required('Delivery date is required'),
    shipping_details: yup.object().shape({
        line1: yup.string().required('Address line 1 is required'),
        line2: yup.string().optional(),
        city: yup.string().required('City is required'),
        state_id: yup.string().required('State is required'),
        zip: yup.string()
            .required('ZIP code is required')
            .matches(/^\d{5}$/, 'ZIP code must be 5 digits'),
        delivery_address_id: yup.string().optional()
    }),
    order_type: yup.string().required('Order type is required'),
    delivery_date_offset: yup.string().optional(),
    isEdit: yup.boolean().optional(),
    id: yup.string().optional()
});

interface ConfirmPoHeaderDetailsProps {
    onClose: () => void;
    onSave: (headerData: any) => void;
    initialData?: any;
    deliveryAddressData: any[];
    referenceData: any;
    states: any[];
    allowedDates: Date[];
    isCalendarOpen: boolean;
    setIsCalendarOpen: (open: boolean) => void;
    disableDeliveryDate: boolean;
    saveUserActivity: () => void;
    handleDateSelect: (date: any) => void;
    saveBomHeaderDetails: () => void;
    handleDeliveryInfoContainerClickAway: () => void;
    handleDeliveryToClick: () => void;
    openDeliveryToDialog: boolean;
    isFocused: boolean;
    containerRef: React.RefObject<any>;
    autocompleteOpen: boolean;
    setAutocompleteOpen: (open: boolean) => void;
    autocompleteOpenLine2: boolean;
    setAutocompleteOpenLine2: (open: boolean) => void;
    autocompleteOpenCity: boolean;
    setAutocompleteOpenCity: (open: boolean) => void;
    stateDropDownValue: string;
    setStateDropDownValue: (value: string) => void;
    stateInputFocus: boolean;
    setStateInputFocus: (focus: boolean) => void;
    handleAutocompleteTabSelection: any;
    formatAddressDisplay: (address: any) => string;
    getDeliveryDateData: (id: string) => Promise<any>;
    setIsCreatePoDirty: (dirty: boolean) => void;
    handleJobPoInputRef: (e: any) => void;
    location: any;
    routes: any;
    setOpenErrorDialog: (open: boolean) => void;
    setErrorMessage: (message: string) => void;
    isDateAllowed: (date: Date) => boolean;
}

const ConfirmPoHeaderDetails: React.FC<ConfirmPoHeaderDetailsProps> = ({
    onClose,
    onSave,
    initialData,
    deliveryAddressData,
    referenceData,
    states,
    allowedDates,
    isCalendarOpen,
    setIsCalendarOpen,
    disableDeliveryDate,
    saveUserActivity,
    handleDateSelect,
    saveBomHeaderDetails,
    containerRef,
    stateDropDownValue,
    setStateDropDownValue,
    handleAutocompleteTabSelection,
    formatAddressDisplay,
    getDeliveryDateData,
    setIsCreatePoDirty,
    handleJobPoInputRef,
    location,
    routes,
    setOpenErrorDialog,
    setErrorMessage,
    isDateAllowed
}) => {
    // Create our own form with validation
    const {
        control,
        register,
        handleSubmit,
        watch,
        setValue,
        getValues,
        formState: { errors, isValid }
    } = useForm({
        resolver: yupResolver(confirmPoHeaderSchema),
        defaultValues: {
            buyer_internal_po: initialData?.buyer_internal_po || '',
            delivery_date: initialData?.delivery_date || '',
            shipping_details: {
                line1: initialData?.shipping_details?.line1 || '',
                line2: initialData?.shipping_details?.line2 || '',
                city: initialData?.shipping_details?.city || '',
                state_id: initialData?.shipping_details?.state_id || '',
                zip: initialData?.shipping_details?.zip || '',
                delivery_address_id: initialData?.shipping_details?.delivery_address_id || ''
            },
            order_type: initialData?.order_type || 'PO',
            delivery_date_offset: initialData?.delivery_date_offset || '',
            isEdit: initialData?.isEdit !== undefined ? initialData.isEdit : true,
            id: initialData?.id || undefined
        },
        mode: 'onChange'
    });

    const jobPoInputRef = useRef(null);
    const addressLine1InputRef = useRef(null);
    const [openDeliveryToDialog, setOpenDeliveryToDialog] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const [stateInputFocus, setStateInputFocus] = useState(false);
    const [autocompleteOpen, setAutocompleteOpen] = useState(false);
    const [autocompleteOpenLine2, setAutocompleteOpenLine2] = useState(false);
    const [autocompleteOpenCity, setAutocompleteOpenCity] = useState(false);
    const { showCommonDialog, resetDialogStore } = useDialogStore();

    useEffect(() => {
        if(jobPoInputRef?.current){
            jobPoInputRef?.current?.focus();
        }
    }, [jobPoInputRef?.current])

    // Local state for calendar
    const [localIsCalendarOpen, setLocalIsCalendarOpen] = useState(false);
    
    const handleOpenCalendarBtn = () => {
        if (!disableDeliveryDate && watch('isEdit')) {
            setLocalIsCalendarOpen(true);
        } else {
            if(disableDeliveryDate){
                setOpenErrorDialog(true)
                setErrorMessage(mobileDiaglogConst.receivingHrsEmpty)
            }
            setLocalIsCalendarOpen(false)
            const nextElement = document.querySelector('[tabindex="15"]');
            if (nextElement instanceof HTMLElement) {
                nextElement.focus();
            }
        }
    }

    const handleLocalDateSelect = (date: any) => {
        handleDateSelect(date);
        // setIsCreatePoDirty(true);
    }

    const handleFormSubmit = (data: any) => {
        onSave(data);
    }

    const handleSaveClick = () => {
        handleSubmit(handleFormSubmit)();
    }

    const handleCancelClick = () => {
        // Reset form to initial data
        // setValue('buyer_internal_po', initialData?.buyer_internal_po || '');
        // setValue('delivery_date', initialData?.delivery_date || '');
        // setValue('shipping_details.line1', initialData?.shipping_details?.line1 || '');
        // setValue('shipping_details.line2', initialData?.shipping_details?.line2 || '');
        // setValue('shipping_details.city', initialData?.shipping_details?.city || '');
        // setValue('shipping_details.state_id', initialData?.shipping_details?.state_id || '');
        // setValue('shipping_details.zip', initialData?.shipping_details?.zip || '');
        // setValue('shipping_details.delivery_address_id', initialData?.shipping_details?.delivery_address_id || '');
        // setValue('order_type', initialData?.order_type || 'PO');
        // setValue('delivery_date_offset', initialData?.delivery_date_offset || '');
        // setValue('isEdit', initialData?.isEdit !== undefined ? initialData.isEdit : true);
        // setValue('id', initialData?.id || undefined);
        
        // // Reset state dropdown value
        // if (initialData?.shipping_details?.state_id) {
        //     const stateName = referenceData?.ref_states?.find((state: any) => state.id === initialData?.shipping_details?.state_id)?.code || '';
        //     setStateDropDownValue(stateName);
        // } else {
        //     setStateDropDownValue('');
        // }
        
        // Close the popup
        onClose();
    }

    const handleConfirmPoJobPoInputRef = (e: any) => {
        jobPoInputRef.current = e;
    }

    
    const handleDeliveryInfoContainerClickAway = () => {
        if(!watch('isEdit')){
            return;
        }
        if (errors?.shipping_details?.zip?.message || errors?.shipping_details?.state_id?.message || errors?.shipping_details?.city?.message || errors?.shipping_details?.line1?.message || errors?.shipping_details?.line2?.message) {
            setOpenDeliveryToDialog(true)
            setIsFocused(true)
        } else {
            setStateInputFocus(false)
            setOpenDeliveryToDialog(false)
            setIsFocused(false)
        }
        if (watch('delivery_date')) {
            const rawDate = watch('delivery_date');              
            // Parse both formats safely
            const parsedDate = dayjs(rawDate, [
              dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit,
              "MM/DD/YYYY hh:mm A" // add your datetime format here
            ], true).toDate();
          
            const isDateValid = isDateAllowed(parsedDate);
                        
            if (!isDateValid) {
              setValue('delivery_date', '');
              showCommonDialog(
                null,
                "Selected date isn’t available for this location. Please choose another date.",
                commomKeys.actionStatus.error,
                resetDialogStore,
                [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]
              );
            }
          }
    }

    const handleDeliveryToClick = () => {
        if(!watch('isEdit')){
            return;
        }
        setOpenDeliveryToDialog(true);
        setIsFocused(true);
    }


    return (
        <div className={styles.headerDetailsContainer}>
            <div className={styles.header}>
                <h2 className={styles.title}>CONFIRM PO HEADER DETAILS</h2>
                <button onClick={handleCancelClick} className={styles.cancelBtn} id='cancelBtn'              onKeyDown={(e: any) => {
                    if(e.key === "Tab"){
                        e.preventDefault();
                        e.stopPropagation();

                        if(!e.shiftKey){
                            const jobPoInputRef = document.querySelector('[tabindex="23"]');
                            if(jobPoInputRef instanceof HTMLElement){
                                jobPoInputRef.focus();
                            }
                        }else{
                            const saveBtn = document.getElementById('save-po-header-details');
                            if(saveBtn instanceof HTMLElement){
                                saveBtn.focus();
                            }   
                        }
                    }
                }}>
                    <span className={styles.cancelText} 
   
                >Cancel</span><CloseIcon /></button>
            </div>
            <div className={clsx(styles.formInputGroup, (isCalendarOpen && watch('isEdit')) && styles.isCalendarOpenDiabledInput)} data-hover-video-id='create-po-header'>
            {(isCalendarOpen) && <div className={styles.calendarOpenOverlay}></div>}
            <div className={styles.createPoHeaderInfoGrid}>
                 <div className={clsx(styles.leftGridHeader, styles.poGridHeader)}>
                <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                    <div className={clsx(styles.col1, styles.poInputMain)}>
                        <label className={styles.pOInputLabel}>JOB / PO#</label>
                        <div className={clsx(watch('buyer_internal_po') && styles.hasValue)}>
                            <InputWrapper>
                            <CustomTextField 
                                className={clsx(styles.inputfiled, styles.pOInput)} 
                                type='text' 
                                register={register("buyer_internal_po")}
                                placeholder='JOB / PO#'
                                autoFocus={true}
                                onBlur={(e: any) => {
                                    e.target.value = e.target.value.trim();
                                    register("buyer_internal_po").onBlur(e);
                                    saveUserActivity();
                                    if ((!getLocal(localStorageKeys.poQuoting, null) && e.target.value) || (getLocal(localStorageKeys.poQuoting, null) && e.target.value !== (getLocal(localStorageKeys.poQuoting, null) as any)?.buyer_internal_po)) {
                                        saveBomHeaderDetails();
                                    }
                                }}
                                onChange={(e: any) => {
                                    setIsCreatePoDirty(true)
                                }}
                                maxLength={20}
                                tabIndex={23}
                                inputRef={handleConfirmPoJobPoInputRef}
                                disabled={!watch('isEdit')}
                                errorInput={errors.buyer_internal_po?.message}
                                id='confirmPoHeaderJobPoInput'
                                onKeyDown={(e: any) => {
                                    if(e.key === "Tab"){
                                        e.preventDefault();
                                        if(!e.shiftKey){
                                            e.stopPropagation();
                                            handleOpenCalendarBtn()
                                            const nextElement = document.querySelector('[tabindex="21"]');
                                            if (nextElement instanceof HTMLElement) {
                                                nextElement.focus();
                                            }
                                        }else{
                                            const cancelBtn = document.getElementById('cancelBtn');
                                            if(cancelBtn instanceof HTMLElement){
                                                cancelBtn.focus();
                                            }
                                        }
                                    }
                                }}
                            />
                        </InputWrapper>
                        </div>
                     
                    </div>

                    <div className={clsx(styles.deliverByContainer, styles.col1)}>
                        <label className={styles.pOInputLabel}>DELIVERY DATE</label>
                        <Calendar allowedDates={allowedDates}
                            value={watch('delivery_date')}
                            setValue={setValue}
                            isCalendarOpen={localIsCalendarOpen}
                            setIsCalendarOpen={setLocalIsCalendarOpen}
                            disableDeliveryDate={disableDeliveryDate || !watch('isEdit')}
                            handleOpenCalendar={handleOpenCalendarBtn}
                            saveUserActivity={saveUserActivity}
                            onDateSelect={handleLocalDateSelect}
                            saveBomHeaderDetails={saveBomHeaderDetails}
                            isConfirmPoCalendar={true}
                            tabIndex={21}
                            onSelectKeyDown={(e) => {                                    
                                const nextElement = document.querySelector('[tabindex="22"]');
                                if (nextElement instanceof HTMLElement && !e.shiftKey) {
                                    nextElement.click();
                                    setTimeout(() => {
                                        handleDeliveryToClick()
                                        addressLine1InputRef?.current?.focus();
                                    }, 100)
                                }
                                if (e?.shiftKey) {
                                    jobPoInputRef?.current?.focus();
                                }
                            }}
                        />
                    </div>
                </div>
                <div className={clsx(styles.formInputGroup1,styles.deliverToContainerMain)}>
                    <label className={styles.pOInputLabel}>DELIVER TO</label>
                        <div
                            className={`${styles.deliverToContainer} ${isFocused ? styles.boxShadow : styles.notEditMode}`}
                            ref={containerRef}
                        >
                            {(openDeliveryToDialog) ?
                                <ClickAwayListener onClickAway={() => handleDeliveryInfoContainerClickAway()}>
                                <span className={styles.deliverToLabel}>
                                    <>
                                        <Controller
                                            name="shipping_details.line1"
                                            control={control}
                                            render={({ field }) => (
                                                <Autocomplete
                                                    disableClearable
                                                    className={clsx(styles.autocompleteContainer, styles.line1Input)}
                                                    options={deliveryAddressData || []}
                                                    value={null}
                                                    inputValue={field.value || ''}
                                                    open={autocompleteOpen && (field.value?.length || 0) > 0}
                                                    onOpen={() => {
                                                        if ((field.value?.length || 0) > 0) {
                                                            setAutocompleteOpen(true);
                                                        }
                                                    }}
                                                    onClose={() => setAutocompleteOpen(false)}
                                                    getOptionLabel={(option: any) => {
                                                        if (typeof option === 'string') return option;
                                                        if (!option) return '';
                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                    }}
                                                    isOptionEqualToValue={(option: any, value: any) => {
                                                        return option?.id === value?.id;
                                                    }}
                                                    filterOptions={(options: any[], { inputValue }) => {
                                                        if (!inputValue) return options;
                                                        const filtered = options.filter(option =>
                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                        );
                                                        return filtered;
                                                    }}
                                                    classes={{
                                                        paper: styles.autocompleteDropdown
                                                    }}
                                                    onInputChange={(event, newInputValue) => {
                                                        field.onChange(newInputValue);
                                                        setIsCreatePoDirty(true);
                                                        if (newInputValue.length > 0) {
                                                            setAutocompleteOpen(true);
                                                        } else {
                                                            setAutocompleteOpen(false);
                                                        }
                                                    }}
                                                    onChange={(event, selectedOption: any) => {
                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                            if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                getDeliveryDateData(selectedOption.id)
                                                            }
                                                            setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                            setValue('shipping_details.city', selectedOption.city);
                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                            const stateName = referenceData?.ref_states?.find((state: any) => state.id === selectedOption?.state_id)?.code || '';
                                                            setValue('shipping_details.state_code', stateName)
                                                            setStateDropDownValue(stateName)
                                                            setIsCreatePoDirty(true);
                                                            saveBomHeaderDetails();
                                                        }
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            autoFocus
                                                            {...params}
                                                            className={clsx(styles.companyNameInput, styles.muiAutocompleteTextField)}
                                                            type='text'
                                                            placeholder='ADDRESS 1'
                                                            inputRef={addressLine1InputRef}
                                                            onBlur={(e: any) => {
                                                                e.target.value = e.target.value.trim();
                                                                field.onBlur();
                                                                saveUserActivity();
                                                                if ((!getLocal(localStorageKeys.poQuoting, null) && e.target.value) || (getLocal(localStorageKeys.poQuoting, null) && e.target.value !== (getLocal(localStorageKeys.poQuoting, null) as any)?.shipping_details?.line1)) {
                                                                    saveBomHeaderDetails();
                                                                }
                                                            }}
                                                            onKeyDown={(e: any) => {
                                                                if (e.key === 'Tab') {
                                                                    if (e.shiftKey) {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        const nextElement = document.querySelector('[tabindex="21"]');
                                                                        if (nextElement instanceof HTMLElement) {
                                                                            setTimeout(() => {
                                                                                nextElement.focus();
                                                                                handleOpenCalendarBtn()
                                                                                handleDeliveryInfoContainerClickAway()
                                                                            }, 300)
                                                                        }
                                                                        // Remove references to undefined variables
                                                                    } else {
                                                                        const autocompleteOptions = document.querySelectorAll('[role="option"]');
                                                                        const hasFilteredOptions = autocompleteOptions.length > 0;
                                                                        if(hasFilteredOptions){
                                                                        handleAutocompleteTabSelection(
                                                                            e,
                                                                            field,
                                                                            autocompleteOpen,
                                                                            ['line1', 'line2', 'city'],
                                                                            setAutocompleteOpen,
                                                                            'input[name="shipping_details.line2"]'
                                                                        );
                                                                        }
                                                                    }
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                    renderOption={(props, option: any) => (
                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                            <div>
                                                                {formatAddressDisplay(option)}
                                                            </div>
                                                        </li>
                                                    )}
                                                    freeSolo
                                                    clearOnEscape
                                                    noOptionsText=""
                                                />
                                            )}
                                        />
                                        <Controller
                                            name="shipping_details.line2"
                                            control={control}
                                            render={({ field }) => (
                                                <Autocomplete
                                                    disableClearable
                                                    options={deliveryAddressData || []}
                                                    value={null}
                                                    inputValue={field.value || ''}
                                                    open={autocompleteOpenLine2 && (field.value?.length || 0) > 0}
                                                    onOpen={() => {
                                                        if ((field.value?.length || 0) > 0) {
                                                            setAutocompleteOpenLine2(true);
                                                        }
                                                    }}
                                                    onClose={() => setAutocompleteOpenLine2(false)}
                                                    getOptionLabel={(option: any) => {
                                                        if (typeof option === 'string') return option;
                                                        if (!option) return '';
                                                        return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                    }}
                                                    isOptionEqualToValue={(option: any, value: any) => {
                                                        return option?.id === value?.id;
                                                    }}
                                                    filterOptions={(options: any[], { inputValue }) => {
                                                        if (!inputValue) return options;
                                                        return options.filter(option =>
                                                            option?.line2?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                            option?.city?.toLowerCase().includes(inputValue.toLowerCase())
                                                        );
                                                    }}
                                                    onInputChange={(event, newInputValue) => {
                                                        field.onChange(newInputValue);
                                                        setIsCreatePoDirty(true);
                                                        if (newInputValue.length > 0) {
                                                            setAutocompleteOpenLine2(true);
                                                        } else {
                                                            setAutocompleteOpenLine2(false);
                                                        }
                                                    }}
                                                    onChange={(event, selectedOption: any) => {
                                                        if (selectedOption && typeof selectedOption === 'object') {
                                                            if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                getDeliveryDateData(selectedOption.id)
                                                            }
                                                            setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                            setValue('shipping_details.line1', selectedOption.line1);
                                                            setValue('shipping_details.line2', selectedOption.line2 || '');
                                                            setValue('shipping_details.city', selectedOption.city);
                                                            setValue('shipping_details.state_id', selectedOption.state_id);
                                                            setValue('shipping_details.zip', selectedOption.zip);
                                                            const stateName = referenceData?.ref_states?.find((state: any) => state.id === selectedOption?.state_id)?.code || '';
                                                            setValue('shipping_details.state_code', stateName)
                                                            setStateDropDownValue(stateName);
                                                            setIsCreatePoDirty(true);
                                                            saveBomHeaderDetails();
                                                        }
                                                    }}
                                                    classes={{
                                                        paper: styles.autocompleteDropdown
                                                    }}
                                                    renderInput={(params) => (
                                                        <TextField
                                                            {...params}
                                                            className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                            type='text'
                                                            placeholder='ADDRESS 2'
                                                            onBlur={(e: any) => {
                                                                e.target.value = e.target.value.trim();
                                                                field.onBlur();
                                                                saveUserActivity();
                                                                saveBomHeaderDetails();
                                                            }}
                                                            onKeyDown={(e: any) => {
                                                                const autocompleteOptions = document.querySelectorAll('[role="option"]');
                                                                const hasFilteredOptions = autocompleteOptions.length > 0;
                                                                if(hasFilteredOptions){
                                                                handleAutocompleteTabSelection(
                                                                    e,
                                                                    field,
                                                                    autocompleteOpenLine2,
                                                                    ['line2', 'line1', 'city'],
                                                                    setAutocompleteOpenLine2,
                                                                    'input[name="shipping_details.city"]'
                                                                );
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                    renderOption={(props, option: any) => (
                                                        <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                            <div>
                                                                {formatAddressDisplay(option)}
                                                            </div>
                                                        </li>
                                                    )}
                                                    freeSolo
                                                    clearOnEscape
                                                    noOptionsText=""
                                                />
                                            )}
                                        />
                                    </>
                                    <span className={styles.lastAddressFiled}>
                                        <span className={styles.addressInputsCol1} >
                                            <Controller
                                                name="shipping_details.city"
                                                control={control}
                                                render={({ field }) => (
                                                    <Autocomplete
                                                        disableClearable
                                                        className={styles.autocompleteContainer}
                                                        options={deliveryAddressData || []}
                                                        value={null}
                                                        inputValue={field.value || ''}
                                                        open={autocompleteOpenCity && (field.value?.length || 0) > 0}
                                                        onOpen={() => {
                                                            if ((field.value?.length || 0) > 0) {
                                                                setAutocompleteOpenCity(true);
                                                            }
                                                        }}
                                                        onClose={() => setAutocompleteOpenCity(false)}
                                                        getOptionLabel={(option: any) => {
                                                            if (typeof option === 'string') return option;
                                                            if (!option) return '';
                                                            return `${option.line1}${option.line2 ? ', ' + option.line2 : ''}, ${option.city}, ${option.zip}`;
                                                        }}
                                                        isOptionEqualToValue={(option: any, value: any) => {
                                                            return option?.id === value?.id;
                                                        }}
                                                        filterOptions={(options: any[], { inputValue }) => {
                                                            if (!inputValue) return options;
                                                            return options.filter(option =>
                                                                option?.city?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                option?.line1?.toLowerCase().includes(inputValue.toLowerCase()) ||
                                                                option?.line2?.toLowerCase().includes(inputValue.toLowerCase())
                                                            );
                                                        }}
                                                        classes={{
                                                            paper: styles.autocompleteDropdown
                                                        }}
                                                        onInputChange={(event, newInputValue) => {
                                                            field.onChange(newInputValue);
                                                            setIsCreatePoDirty(true);
                                                            if (newInputValue.length > 0) {
                                                                setAutocompleteOpenCity(true);
                                                            } else {
                                                                setAutocompleteOpenCity(false);
                                                            }
                                                        }}
                                                        onChange={(event, selectedOption: any) => {
                                                            if (selectedOption && typeof selectedOption === 'object') {
                                                                if (selectedOption.id !== watch('shipping_details.delivery_address_id')) {
                                                                    getDeliveryDateData(selectedOption.id)
                                                                }
                                                                setValue('shipping_details.delivery_address_id', selectedOption.id);
                                                                setValue('shipping_details.line1', selectedOption.line1);
                                                                setValue('shipping_details.line2', selectedOption.line2 || '');
                                                                setValue('shipping_details.city', selectedOption.city);
                                                                setValue('shipping_details.state_id', selectedOption.state_id);
                                                                setValue('shipping_details.zip', selectedOption.zip);
                                                                const stateName = referenceData?.ref_states?.find((state: any) => state.id === selectedOption?.state_id)?.code || '';
                                                                setValue('shipping_details.state_code', stateName)
                                                                setStateDropDownValue(stateName);
                                                                setIsCreatePoDirty(true);
                                                                saveBomHeaderDetails();
                                                            }
                                                        }}
                                                        renderInput={(params) => (
                                                            <TextField
                                                                {...params}
                                                                className={clsx(styles.addressInputs, styles.muiAutocompleteTextField)}
                                                                type='text'
                                                                placeholder='CITY'
                                                                onBlur={(e: any) => {
                                                                    e.target.value = e.target.value.trim();
                                                                    field.onBlur();
                                                                    saveUserActivity();
                                                                    saveBomHeaderDetails();
                                                                }}
                                                                onKeyDown={(e: any) => {
                                                                    const autocompleteOptions = document.querySelectorAll('[role="option"]');
                                                                    const hasFilteredOptions = autocompleteOptions.length > 0;
                                                                    if(hasFilteredOptions){
                                                                    handleAutocompleteTabSelection(
                                                                        e,
                                                                        field,
                                                                        autocompleteOpenCity,
                                                                        ['city', 'line1', 'line2'],
                                                                        setAutocompleteOpenCity,
                                                                        'input[name="shipping_details.zip"]'
                                                                    );
                                                                    }
                                                                }}
                                                            />
                                                        )}
                                                        renderOption={(props, option: any) => (
                                                            <li {...props} key={option?.id || `${option?.line1}-${option?.city}-${option?.zip}`}>
                                                                <div>
                                                                    {formatAddressDisplay(option)}
                                                                </div>
                                                            </li>
                                                        )}
                                                        freeSolo
                                                        clearOnEscape
                                                        noOptionsText=""
                                                    />
                                                )}
                                            />
                                        </span>

                                        <span className={clsx(styles.addressInputsCol2, errors?.shipping_details?.state_id?.message && styles.errorInput,
                                            stateInputFocus && styles.selectShade)}>
                                            {stateInputFocus && <>
                                                <div className={styles.shape1}></div>
                                                <div className={styles.shape2}></div>
                                            </>}
                                            <StateDropDown
                                                states={states}
                                                setValue={setValue}
                                                stateDropDownValue={stateDropDownValue}
                                                setStateDropDownValue={setStateDropDownValue}
                                                setStateInputFocus={setStateInputFocus}
                                                stateInputFocus={stateInputFocus}
                                            />
                                        </span>
                                        <span className={styles.addressInputsCol3}>
                                            <Tooltip
                                                title={errors?.shipping_details?.zip?.message}
                                                arrow
                                                placement={"top-end"}
                                                disableInteractive
                                                TransitionComponent={Fade}
                                                TransitionProps={{ timeout: 200 }}
                                                classes={{
                                                    tooltip: 'stateTooltip',
                                                }}
                                            >
                                                <div>
                                                    <InputWrapper>
                                                        <CustomTextField 
                                                            className={clsx(styles.addressInputs, errors?.shipping_details?.zip?.message && styles.errorInput)} 
                                                            type='text'
                                                            register={register("shipping_details.zip")}
                                                            placeholder='ZIP'
                                                            onChange={(e: any) => {
                                                                register("shipping_details.zip").onChange(e);
                                                                const zipCode = e.target.value.replace(/\D/g, '');
                                                                setValue("shipping_details.zip", zipCode);
                                                            }}
                                                            maxLength={5}
                                                            onBlur={(e: any) => {
                                                                e.target.value = e.target.value.trim();
                                                                register("shipping_details.zip").onBlur(e);
                                                                saveUserActivity();
                                                                saveBomHeaderDetails();
                                                            }}
                                                            onKeyDown={(e: any) => {
                                                                if (e.key === 'Tab') {
                                                                    e.stopPropagation();
                                                                    e.preventDefault();
                                                                    handleDeliveryInfoContainerClickAway()
                                                                    if (!e.shiftKey) {
                                                                        const saveBtn = document.getElementById('save-po-header-details');
                                                                        const cancelBtn = document.getElementById('cancelBtn');
                                                                        if(saveBtn?.disabled){
                                                                            if(cancelBtn instanceof HTMLElement){
                                                                                cancelBtn.focus();
                                                                            }
                                                                        }else{
                                                                            if(saveBtn instanceof HTMLElement){
                                                                                saveBtn.focus();
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }}
                                                            errorInput={errors?.shipping_details?.zip?.message}
                                                        />
                                                    </InputWrapper>
                                                </div>
                                            </Tooltip>
                                        </span>
                                    </span>
                                </span>
                                </ClickAwayListener> 
                                :
                                <div className={clsx(styles.deliverToLabel, styles.staticDeliverTo)}
                                onClick={handleDeliveryToClick}
                                tabIndex={22}
                                onFocus={handleDeliveryToClick}
                                >
                                    {
                                        (watch('shipping_details.line1') || watch('shipping_details.line2') || (watch('shipping_details.city') || stateDropDownValue || watch('shipping_details.zip'))) ? (<>
                                            <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line1') ? `${watch('shipping_details.line1')}` : ''}</p>
                                            <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('shipping_details.line2') ? `${watch('shipping_details.line2')}` : ''}</p>
                                            <span className={styles.lastAddressFiled}>
                                                <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('shipping_details.city') ? `${watch('shipping_details.city')}` : ''}</p>
                                                <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{stateDropDownValue ? stateDropDownValue : ''}</p>
                                                <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('shipping_details.zip') ? `${watch('shipping_details.zip')}` : ''}</p>
                                            </span>
                                        </>) : (<span>DELIVER TO</span>)
                                    }
                                </div>
                            }
                        </div>
                </div>
                 </div>
            </div>
            </div>
              <div className={styles.footerPopup}>
                <button className={styles.saveButton}  onClick={handleSaveClick} id="save-po-header-details"
                onKeyDown={(e: any) => {
                    if(e.key === "Tab"){
                        e.preventDefault();
                        e.stopPropagation();
                        if(e.shiftKey){
                            setTimeout(() => {
                                handleDeliveryToClick()
                                addressLine1InputRef?.current?.focus();
                            }, 100)
                        }else{
                            const cancelBtn = document.getElementById('cancelBtn');
                            if(cancelBtn instanceof HTMLElement){
                                cancelBtn.focus();
                            }
                        }
                    }
                }}
                    disabled={!isValid}>SAVE</button>
              </div>
           
        </div>
    );
};

export default ConfirmPoHeaderDetails;
