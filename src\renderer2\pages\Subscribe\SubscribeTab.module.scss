.subscribeTab {
    width: 100%;
    // height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #191a20;

    &.subscriptionCompleted {
        border: solid 1px #32ff6c;
        border-top: 0px;
        border-radius: 0px 0px 13px 13px;
        padding: 16px;
    }

    .licenseDetailHeader.licenseDetailHeader {
        display: flex;
        justify-content: space-between;

        .stepTitle {
            font-family: Syncopate;
            font-size: 14px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            text-transform: uppercase;
        }

        .licenseCount {
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.3;
            letter-spacing: -0.64px;
            text-align: right;
            color: #32ccff;
            text-transform: uppercase;

            span {
                font-weight: normal;
            }
        }

    }

    .licenseDetailContainer {
        display: flex;
        column-gap: 16px;
        margin: 24px 0px;

        .licenseDetailBox {
            padding: 16px;
            border-radius: 13px;
            background-color: #222329;
            min-height: 212px;

            &.needMoreLicensesBox {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 40px 22px 16px 22px;

                .licenseDetailText1 {
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #fff;
                    margin-bottom: 10px;
                    text-transform: uppercase;
                }

                .licenseDetailText2 {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: #fff;
                }

                button.goToSettingsButton {
                    width: 100%;
                    height: 40px;
                    flex-grow: 0;
                    display: flex;
                    flex-direction: row;
                    justify-content: center;
                    align-items: flex-start;
                    gap: 8px;
                    padding: 12px 0 0;
                    border-radius: 10px;
                    border: solid 1px rgba(255, 255, 255, 0.16);
                    background-color: #222329;
                    font-family: Syncopate;
                    font-size: 14px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.3;
                    letter-spacing: -0.56px;
                    text-align: center;
                    margin-top: auto;
                    color: #71737f;
                    text-transform: uppercase;
                    transition: all 0.1s;

                    &:hover {
                        box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.8);
                        background-image: linear-gradient(128deg, #1c40e7 -23%, #16b9ff 116%);
                        background-color: transparent;
                        color: #fff;
                        border: 1px solid transparent;
                    }
                }
            }

            &.successMessage {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 40px 22px 16px 22px;

                .successMessageTitle {
                    font-family: Syncopate;
                    font-size: 20px;
                    font-weight: bold;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1;
                    letter-spacing: 2px;
                    text-align: center;
                    color: #fff;
                }

                .successMessageText {
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: #c3c4ca;
                    margin: 8px 0px;
                }

                .successMessageText1 {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.4;
                    letter-spacing: normal;
                    text-align: center;
                    color: #fff;
                }
            }

            &:nth-child(1) {
                flex: 25%;
            }

            &:nth-child(2) {
                flex: 50%;
            }

            &:nth-child(3) {
                flex: 25%;
            }
        }
    }

    .grid {
        display: flex;
        gap: 25px;
        width: 100%;

        .equalSpacing {
            flex: 1;
        }
    }



    .licenseDetailText {
        font-family: Syncopate;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.3;
        letter-spacing: -0.64px;
        text-align: right;
        color: #32ccff;
    }


    .nextPaymentDetailContainer {
        display: flex;
        justify-content: space-between;
        column-gap: 30px;
        width: 100%;
        margin: 24px 0px;

        .licenseDetail {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 13px;
            background-color: rgba(50, 204, 255, 0.04);

            .licenseDetailText {
                font-family: Syncopate;
                font-size: 18px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: 0.72px;
                text-align: center;
                color: #32ccff;
                span{
                    font-weight: normal;
                }
            }

        }
    }

    .subscriptionActions {
        width: 100%;

        .actionbarTitle {
            display: flex;
            flex-direction: column;

            .title {
                font-family: Syncopate;
                font-size: 16px;
                font-weight: bold;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.3;
                letter-spacing: -0.64px;
                text-align: left;
                color: #fff;
            }

            .description {
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: left;
                color: #c3c4ca;
                margin-top: 4px;
            }
        }

        .actionbarButtons {
            display: flex;
            flex-direction: row-reverse;
            gap: 8px;

            .actionbarButton {
                padding: 8px 14px;
                border-radius: 500px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Inter;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: normal;
                text-align: center;
                color: #71737f;

                &:hover {
                    color: #fff;
                }
            }
        }
    }

    .subscribeUserTable {
        margin-top: 24px;
        width: 100%;
        flex: 1;

    }
}

.editPaymentInfoPopup {
    .popupContent {
        padding: 32px;
        border-radius: 16px;
        box-shadow: 0 0 67.4px 4px #000;
        background-color: #222329;
    }

    &.editLicensePopup {
        .popupContent {
            max-width: 544px;
            padding: 32px;
            max-height: 100%;
        }
    }

    &.uploadUserListPopup {
        .popupContent {
            max-width: 460px;
            padding: 24px 24px 46px 24px;
        }
    }
}